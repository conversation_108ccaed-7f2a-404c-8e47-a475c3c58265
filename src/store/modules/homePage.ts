import { defineStore } from 'pinia'
import type { GLTF }  from 'three/examples/jsm/loaders/GLTFLoader.js'
import * as THREE from 'three'

export const useHomePage = defineStore('homePage', {
    state: () => ({
        indexGltf: undefined as GLTF,
        filterRoomGltf: undefined as GLTF,
        cleanWaterBasinGltf: undefined as GLTF,
        flocculationSettlingRoomGltf: undefined as GLTF,
        waterPumpHouseGltf: undefined as GLTF,
        chlorinationDosingRoomGltf: undefined as GLTF,
        hdr: undefined as THREE.DataTexture,
    }),
    actions: {
        setIndexModel(gltf: GLTF) {
            this.indexGltf = gltf
        },
        setFilterRoomModel(gltf: GLTF) {
            this.filterRoomGltf = gltf
        },
        setCleanWaterBasinModel(gltf: GLTF) {
            this.cleanWaterBasinGltf = gltf
        },
        setFlocculationSettlingRoomModel(gltf: GLTF) {
            this.flocculationSettlingRoomGltf = gltf
        },
        setWaterPumpHouseModel(gltf: GLTF) {
            this.waterPumpHouseGltf = gltf
        },
        setChlorinationDosingRoomModel(gltf: GLTF) {
            this.chlorinationDosingRoomGltf = gltf
        },
        setHdr(hdr: THREE.DataTexture) {
            this.hdr = hdr
        },
    },
})