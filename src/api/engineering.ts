import { defHttp } from '/@/utils/http/axios'
import type { RequestOptions } from '/#/axios'

export interface waterSelectItem {
    id: string
    name: string
}

/**
 * 字典格式
 */
export interface dictionaries {
    value: string
    label: string
}

/**
 * 获取水厂列表数据
 */
export const getSelect = (params?: any, requestOptions?: RequestOptions) => {
    return defHttp.get({
        url: '/xz-ep/ep/waterWorks/getList',
        params,
    }, requestOptions)
}

/**
 * 通过设备大类code获取设备分类
 */
export const getSmallCategory = (params: any, requestOptions?: RequestOptions) => {
    return defHttp.get({
        url: '/xz-system/sys/dict/getDeviceCategory',
        params,
    }, requestOptions)
}

/**
 * 根据code查询业务列表
 */
export const getSystematicName = (params: any, requestOptions?: RequestOptions) => {
    return defHttp.get({
        url: '/xz-ep/ep/iotSystem/getBizList',
        params,
    }, requestOptions)
}

/**
 * 查询子节点数据
 */
export const getChildList = (params: any, requestOptions?: RequestOptions) => {
    return defHttp.get({
        url: '/xz-ep/ep/iotVariable/listBindVar',
        params,
    }, requestOptions)
}

/**
 * 新增表格1级数据
 */
export const postTreeFatherAdd = (data: any, requestOptions?: RequestOptions) => {
    return defHttp.post({
        url: '/xz-ep/ep/iotSystem/add',
        data,
    }, requestOptions)
}

/**
 * 编辑表格1级数据
 */
export const putTreeFatherAdd = (data: any, requestOptions?: RequestOptions) => {
    return defHttp.put({
        url: '/xz-ep/ep/iotSystem/edit',
        data,
    }, requestOptions)
}

/**
 * 查询表格1级数据
 */
export const getTreeFather = (params?: any, requestOptions?: RequestOptions) => {
    return defHttp.get({
        url: '/xz-ep/ep/iotSystem/list',
        params,
    }, requestOptions)
}

/**
 * 查询嵌套表格2级数据
 */
export const getChildren = (params: any, requestOptions?: RequestOptions) => {
    return defHttp.get({
        url: '/xz-ep/ep/iotVariable/listBindVar',
        params,
    }, requestOptions)
}

/**
 * 查询嵌套表格2级数据所有可能存在的子级，进行选择与配置
 */
export const getChildrenList = (params: any, requestOptions?: RequestOptions) => {
    return defHttp.get({
        url: '/xz-ep/ep/iotVariable/list4Bind',
        params,
    }, requestOptions)
}

/**
 * 增加嵌套表格2级数据
 */
export const postChildrenList = (data: any, requestOptions?: RequestOptions) => {
    return defHttp.post({
        url: '/xz-ep/ep/iotSystem/bindingSystemVar',
        data,
    }, requestOptions)
}

/**
 * 嵌套表格2级 变量编排-根据组态页面排序
 */
export const getSort = (params: any, requestOptions?: RequestOptions) => {
    return defHttp.get({
        url: '/xz-ep/ep/iotSystem/sort',
        params,
    }, requestOptions)
}

/**
 * 嵌套表格2级 单个系统，变量取消绑定
 */
export const getUntie = (params: any, requestOptions?: RequestOptions) => {
    return defHttp.get({
        url: '/xz-ep/ep/iotSystem/cancelBinding',
        params,
    }, requestOptions)
}

/**
 * 表格1级 删除本父级
 */
export const deleFather = (params: any, requestOptions?: RequestOptions) => {
    return defHttp.delete({
        url: '/xz-ep/ep/iotSystem/delete?id=' + params.id,
    }, requestOptions)
}

/**
 * 获取 新增 时，穿梭框数据(巡检点管理-获取所有巡检点不分页)。
 * 所有穿梭框都是同一个接口
 */
export const getTransfer = (params?: any, requestOptions?: RequestOptions) => {
    return defHttp.get({
        url: '/xz-ep/ep/patrolPoint/queryList',
        params,
    }, requestOptions)
}

/**
 * 获取 新增 时，穿梭框数据(巡检点管理-获取所有巡检点不分页)。
 * 所有穿梭框都是同一个接口
 */
export const postInspectionTaskAdd = (data: any, requestOptions?: RequestOptions) => {
    return defHttp.post({
        url: '/xz-ep/ep/patrolRecord/add',
        data,
    }, requestOptions)
}

/**
 * 获取表格数据
 * 巡检任务-分页列表查询
 */
export const getInspectionTaskTable = (params?: any, requestOptions?: RequestOptions) => {
    return defHttp.get({
        url: '/xz-ep/ep/patrolRecord/list',
        params,
    }, requestOptions)
}

/**
 * 流程图 表格
 * 流程记录-通过巡检任务Id获取流程记录
 */
export const getInspectionTaskFlowTable = (params: any, requestOptions?: RequestOptions) => {
    return defHttp.get({
        url: '/xz-ep/ep/flowRecord/getRecordListByTaskId',
        params,
    }, requestOptions)
}

/**
 * 
 * 巡检流程操作:2派发、3领取、4巡检、5完成、6评估、0取消
 */
export const postInspectionTaskCancellation = (data: any, requestOptions?: RequestOptions) => {
    return defHttp.post({
        url: '/xz-ep/ep/patrolRecord/updateTask',
        data,
    }, requestOptions)
}

/**
 * 获取 接受派发人员 下拉框
 * 获取用户列表不分页
 */
export const getDistributeSelect = (params?: any, requestOptions?: RequestOptions) => {
    return defHttp.get({
        url: '/sys/user/getUserAllList',
        params,
    }, requestOptions)
}

/**
 * 获取 接受派发人员 下拉框
 * 获取用户列表不分页
 */
export const deleteDistribute = (params: any, requestOptions?: RequestOptions) => {
    return defHttp.delete({
        url: '/xz-ep/ep/patrolRecord/delete?id=' + params.id,
    }, requestOptions)
}

/**
 * 获取 编辑框内的 穿梭框
 * 获取巡检任务详情包括巡检明细列表
 */
export const getTransferEdit = (params: any, requestOptions?: RequestOptions) => {
    return defHttp.get({
        url: '/xz-ep/ep/patrolRecord/getOnePatrol',
        params,
    }, requestOptions)
}

/**
 * 获取 编辑框内的 穿梭框
 * 获取巡检任务详情包括巡检明细列表
 */
export const putInspectionTaskEdit = (data: any, requestOptions?: RequestOptions) => {
    return defHttp.put({
        url: '/xz-ep/ep/patrolRecord/edit',
        data,
    }, requestOptions)
}

/**
 * 获取 巡检记录
 * 获取巡检记录详情包括巡检明细列表
 */
export const getInspectionTaskDetailsChildern = (params: any, requestOptions?: RequestOptions) => {
    return defHttp.get({
        url: '/xz-ep/ep/patrolRecord/getOnePatrol',
        params,
    }, requestOptions)
}

/**
 * 获取 实时数据 配置 表格数据
 * IOT变量-不分页列表查询
 */
export const getNowDataTable = (params?: any, requestOptions?: RequestOptions) => {
    return defHttp.get({
        url: '/xz-ep/ep/iotVariable/listNoPage',
        params,
    }, requestOptions)
}

/**
 * 获取 实时数据 配置 表格数据
 * IOT变量-不分页列表查询
 */
export const getNowDataSelect = (params?: any, requestOptions?: RequestOptions) => {
    return defHttp.get({
        url: '/xz-ep/ep/relationMonitor/getList',
        params,
    }, requestOptions)
}

/**
 * 获取 实时数据 echarts 图表数据
 * 实时数据--web端
 */
export const postNowEcharts = (data: any, requestOptions?: RequestOptions) => {
    return defHttp.post({
        url: '/xz-ep/data_monitor/getCurrentData',
        data,
    }, requestOptions)
}

/**
 * 获取 历史数据 echarts 图表数据
 * 历史数据--曲线--web端
 */
export const postHistoryEcharts = (data: any, requestOptions?: RequestOptions) => {
    return defHttp.post({
        url: '/xz-ep/data_monitor/getHisDataLine',
        data,
    }, requestOptions)
}

/**
 * 获取 历史数据 表格数据
 * 历史数据--分页表格--web端
 */
export const postHistoryTable = (data: any, requestOptions?: RequestOptions) => {
    return defHttp.post({
        url: '/xz-ep/data_monitor/getHisDataTable',
        data,
    }, requestOptions)
}

/**
 * 获取 萤石云 视频token
 */
export const postVideoToken = (data?: any, requestOptions?: RequestOptions) => {
    return defHttp.post({
        url: '/xz-ep/ep/videoDevice/queryVideoToken',
        data,
    }, {
        ...requestOptions,
        successMessageMode: 'none'
    })
}

/**
 * 获取 萤石云 视频列表
 */
export const getVideoList = (params?: any, requestOptions?: RequestOptions) => {
    return defHttp.get({
        url: '/xz-ep/ep/videoDevice/getVideoGroupList',
        params,
    }, requestOptions)
}

/**
 * 获取 监测点 下拉列表
 * 获取管网监测点列表App
 */
export const getMonitoringPointSelectList = (params?: any, requestOptions?: RequestOptions) => {
    return defHttp.get({
        url: '/xz-ep/ep/relationMonitor/getPointApp',
        params,
    }, requestOptions)
}

/**
 * 获取 监测点 数据
 * 监测点-获取监测点数据详情
 */
export const getMonitoringPointData = (params: any, requestOptions?: RequestOptions) => {
    return defHttp.get({
        url: '/xz-ep/ep/relationMonitor/getDetail4Gis',
        params,
    }, requestOptions)
}

/**
 * 设置 压力&阀门&液位 数据
 * 控制（监测点详情处使用）
 */
export const postSetMonitoringPoint = (data: any, requestOptions?: RequestOptions) => {
    data.sysFlag = 'web'

    return defHttp.post({
        url: '/xz-ep/ep/ctrl',
        data,
    }, requestOptions)
}

/**
 * 获取 拓扑数据
 * 监测点-获取检测点拓扑数据
 */
export const getTopology = (params?: any, requestOptions?: RequestOptions) => {
    return defHttp.get({
        url: '/xz-ep/ep/relationMonitor/topology',
        params,
    }, requestOptions)
}

/**
 * 复制 操作
 * 系统变量映射-添加
 */
export const copySon = (data: any, requestOptions?: RequestOptions) => {
    return defHttp.post({
        url: '/xz-ep/ep/iotSystemVarRef/add',
        data,
    }, requestOptions)
}

/**
 * 获取水厂介绍
 * 水厂信息-通过id查询
 */
export const getWaterPanelInfo = (params: any, requestOptions?: RequestOptions) => {
    return defHttp.get({
        url: '/xz-ep/ep/waterWorks/queryById',
        params,
    }, requestOptions)
}

/**
 * 获取取水管网
 * 管线段-获取大屏管网信息
 */
export const getWaterIntakeNetworkPanelInfo = (params?: any, requestOptions?: RequestOptions) => {
    return defHttp.get({
        url: '/xz-ep/ep/relation/getScreenInfo',
        params,
    }, requestOptions)
}

/**
 * 获取 报警规则 表格数据
 * 报警规则-分页列表查询
 */
export const getAlarmRuleList = (params?: any, requestOptions?: RequestOptions) => {
    return defHttp.get({
        url: '/xz-ep/ep/warningRule/list',
        params,
    }, requestOptions)
}

/**
 * 新增 报警规则 表格数据
 * 报警规则-添加
 */
export const postAlarmRuleAdd = (data: any, requestOptions?: RequestOptions) => {
    return defHttp.post({
        url: '/xz-ep/ep/warningRule/add',
        data,
    }, requestOptions)
}

/**
 * 获取 所有用户信息 所有推送人
 * 获取用户列表不分页
 */
export const getUser = (params?: any, requestOptions?: RequestOptions) => {
    return defHttp.get({
        url: '/xz-system/sys/user/getUserAllList',
        params,
    }, requestOptions)
}

/**
 * 删除 表格 行
 * 报警规则-通过id删除
 */
export const deleteAlarmRule = (params: any, requestOptions?: RequestOptions) => {
    return defHttp.delete({
        url: '/xz-ep/ep/warningRule/delete?id=' + params.id,
        params,
    }, requestOptions)
}

/**
 * 报警规则-编辑
 */
export const postEditAlarmRule = (data: any, requestOptions?: RequestOptions) => {
    return defHttp.post({
        url: '/xz-ep/ep/warningRule/edit',
        data,
    }, requestOptions)
}

/**
 * 获取减压池 tab
 * 获取减压池系统编号列表
 */
export const getSeleDecompressionPool = (params?: any, requestOptions?: RequestOptions) => {
    return defHttp.get({
        url: '/xz-ep/ep/iotSystem/getPoolByType',
        params,
    }, requestOptions)
}

/**
 * 获取 月漏损
 * 漏损分析：漏损数据
 */
export const getLeakage = (params: any, requestOptions?: RequestOptions) => {
    return defHttp.get({
        url: '/xz-etl/etl/leakage/getLeakageData',
        params,
    }, requestOptions)
}

/**
 * 获取 漏损详情
 * 漏损分析-获取单个漏损详情
 */
export const getLeakageDetails = (params: any, requestOptions?: RequestOptions) => {
    return defHttp.get({
        url: '/xz-etl/etl/leakage/getOneDetail',
        params,
    }, requestOptions)
}

/**
 * 获取 报警规则模板
 * 消息模板-获取消息模板列表
 */
export const getAlarmRuleTemplate = (params?: any, requestOptions?: RequestOptions) => {
    return defHttp.get({
        url: '/xz-ep/ep/messageTemp/getList',
        params,
    }, requestOptions)
}

/**
 * 获取 比较方式
 * 报警规则-列表查询报警规则比较方式
 */
export const getComparisonMode = (params?: any, requestOptions?: RequestOptions) => {
    return defHttp.get({
        url: '/ep/warningRule/queryComparisonMethodList',
        params,
    }, requestOptions)
}

/**
 * 编辑 报警规则状态
 * 报警规则-编辑状态
 */
export const putAlarmRuleState = (data: any, requestOptions?: RequestOptions) => {
    return defHttp.put({
        url: '/xz-ep/ep/warningRule/editStatus',
        data,
    }, requestOptions)
}

/**
 * 报表中心
 * 报表接口
 */
export const getReportCenter = (params?: any, requestOptions?: RequestOptions) => {
    return defHttp.get({
        url: '/xz-ep/data_monitor/getReport',
        params,
    }, requestOptions)
}

/**
 * 报表中心——水厂选择
 * 水厂信息-获取水厂下拉列表
 */
export const getWater = (params?: any, requestOptions?: RequestOptions) => {
    return defHttp.get({
        url: '/xz-ep/ep/waterWorks/getList',
        params,
    }, requestOptions)
}
