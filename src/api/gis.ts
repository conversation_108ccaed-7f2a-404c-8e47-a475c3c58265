import { defHttp } from '/@/utils/http/axios'
import type { RequestOptions } from '/#/axios'


/**
 * 获取树数据
 */
export const getTreeList = (params?: any, requestOptions?: RequestOptions) => {
    return defHttp.get({
        url: '/ep/gis/getTreeGisList',
        params,
    }, requestOptions)
}

/**
 * 获取绘制数据
 */
export const getDraw = (params?: any, requestOptions?: RequestOptions) => {
  return defHttp.get({
    url: '/ep/relation/getGisList',
    params,
  }, requestOptions)
}

/**
 * 获取监测点数据
 */
export const getMonitor = (params?: any, requestOptions?: RequestOptions) => {
  return defHttp.get({
    url: '/ep/gis/getGisList',
    params,
  }, requestOptions)
}

/**
 * 获取监测点详情
 */
export const getMonitorInfo = (params?: any, requestOptions?: RequestOptions) => {
  return defHttp.get({
    url: '/ep/gis/getOneDataById',
    params,
  }, requestOptions)
}
