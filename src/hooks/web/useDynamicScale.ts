import { computed } from 'vue'
import { useMenuSetting } from '/@/hooks/setting/useMenuSetting'

/**
 * 用于 YSXK-ScaleDiv 组件的动态缩放 Hook
 * 根据侧边栏折叠状态动态计算 reduceW 和 w 值
 */
export function useDynamicScale() {
  // 获取菜单设置，用于判断侧边栏折叠状态
  const { getCollapsed } = useMenuSetting()

  // 动态计算 reduceW 值
  // 当侧边栏折叠时为 48px，展开时为 210px
  const dynamicReduceW = computed(() => {
    return getCollapsed.value ? 48 : 210
  })

  // 动态计算 w 值
  // 当侧边栏折叠时为 1872px，展开时为 1710px
  const dynamicW = computed(() => {
    return getCollapsed.value ? 1872 : 1710
  })

  return {
    dynamicReduceW,
    dynamicW
  }
}
