import { useUserStore } from '/@/store/modules/user'

export default class <T> {
    /**
     * WebSocket 实例
     */
    private webSocket: WebSocket
    /**
     * 错误重连计时器
     */
    private reconnectTimeout: NodeJS.Timeout
    /**
     * 防止链接错误多次重连
     */
    private lockReconnect = true
    /**
     * 保存的数据
     */
    ckData: T
    /**
     * 心跳检测定时器
     */
    private heartbeatInterval: NodeJS.Timeout
    /**
     * 心跳检测超时计时器
     */
    private heartbeatTimeout: NodeJS.Timeout
    /**
     * 心跳检测间隔，10秒
     */
    private HEARTBEAT_INTERVAL = 10 * 1000
    /**
     * 心跳检测超时时间，100秒
     */
    private HEARTBEAT_TIMEOUT = 100 * 1000
    /**
     * 请求的完整地址
     */
    private url: string
    /**
     * 组装数据 请求成功回调方法
     */
    private assemblyData: Function

    constructor(websocketName: string, systemNo: string, assemblyData: Function) {
        this.assemblyData = assemblyData

        const userStore = useUserStore()

        const userId = userStore.userInfo.id
        this.url = import.meta.env.VITE_GLOB_DOMAIN_URL + '/' + websocketName + '/' + userId + '/' + systemNo
        console.log('当前url：', this.url)
        this.initWebSocket()
    }

    private initWebSocket() {
        this.webSocket = new WebSocket(this.url)

        /**
         * WebSocket 通信成功
         */
        this.webSocket.onopen = (e) => {
            // console.log("WebSocket连接成功")
            // 清除重连定时器
            this.clearReconnect()
            // 开始心跳检测
            this.startHeartbeat()
            console.log('WebSocket 通信成功：', e)
        }

        /**
         * WebSocket 通信错误
         */
        this.webSocket.onerror = (e) => {
            // console.log("WebSocket连接发生错误")
            // 开始重连
            this.reconnect()
            console.log('WebSocket 通信错误：', e)
        }

        /**
         * WebSocket 每次数据更新
         */
        this.webSocket.onmessage = (e) => {
            // console.log("-----接收消息-------",e.data)
            let data = eval("(" + e.data + ")") //解析对象
            // 保存数据对象
            this.ckData = data
            // 开始组装数据
            this.assemblyData()
            // 收到消息时，重置心跳检测的超时时间 后端正常情况 60 秒 返回一次 100 秒 前端超时
            this.resetHeartbeat()
        }

        /**
         * WebSocket 关闭后 被动关闭 以及 主动关闭
         */
        this.webSocket.onclose = (e) => {
            console.log('WebSocket 关闭后 被动关闭 以及 主动关闭：', e)
        }
    }
    /**
     * 错误重连
     */
    private reconnect() {
        if (this.lockReconnect) {
            this.lockReconnect = false
            // 延迟五秒重连一次，如果失败再重连一次
            this.reconnectTimeout = setTimeout(() => {
                this.lockReconnect = true
                // console.info("尝试重连...")
                this.initWebSocket()
            }, 5000)
        }
    }
    /**
     * 心跳检测 10秒
     */
    private startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            if (this.webSocket.readyState === WebSocket.OPEN) {
                this.webSocket.send('ping') // 发送心跳消息
            }
        }, this.HEARTBEAT_INTERVAL)
        this.heartbeatTimeOutResponse()
    }
    /**
     * 心跳检测超时响应 100秒
     */
    private heartbeatTimeOutResponse() {
        this.heartbeatTimeout = setTimeout(() => {
            // console.log('心跳检测超时，关闭 WebSocket 连接')
            // 关闭当前 WebSocket
            this.closeWebSocket()
            // 错误重连
            this.reconnect()
        }, this.HEARTBEAT_TIMEOUT)
    }
    /**
     * 关闭 WebSocket 连接 主动关闭
     */
    closeWebSocket() {
        this.webSocket.close(1000)
        // 清除计时器
        this.clearHeartbeat()
        this.clearReconnect()
    }
    /**
     * 清除心跳检测定时器
     */
    private clearHeartbeat() {
        clearInterval(this.heartbeatInterval)
        clearTimeout(this.heartbeatTimeout)
    }
    /**
     * 重置心跳检测的超时时间
     */
    private resetHeartbeat() {
        // 清除原有的
        clearTimeout(this.heartbeatTimeout)
        // 创建一个新的
        this.heartbeatTimeOutResponse()
    }
    /**
     * 清除错误重连定时器
     */
    private clearReconnect() {
        clearTimeout(this.reconnectTimeout)
    }
}