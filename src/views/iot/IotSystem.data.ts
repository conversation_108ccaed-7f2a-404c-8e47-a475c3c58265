import {BasicColumn} from '/@/components/Table';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '水厂',
    align: "center",
    dataIndex: 'waterId_dictText'
  },
  {
    title: '编码',
    align: "center",
    dataIndex: 'systemNo'
  },
  {
    title: '类别',
    align: "center",
    dataIndex: 'type_dictText'
  },
  {
    title: '系统名称',
    align: "center",
    dataIndex: 'name'
  },
  {
    title: '备注',
    align: "center",
    dataIndex: 'remark'
  },
  {
    title: '操作',
    align: "center",
    key: 'operation',
    fixed: 'right',
    width: 150,
  },
]

// 高级查询数据
export const superQuerySchema = {
  waterId: {title: '水厂',order: 0,view: 'list', type: 'string',dictTable: "water_works", dictCode: 'id', dictText: 'name',},
  systemNo: {title: '编码',order: 1,view: 'text', type: 'string',},
  type: {title: '类别',order: 2,view: 'list', type: 'string',dictCode: 'iot_system_type',},
  name: {title: '系统名称',order: 3,view: 'text', type: 'string',},
  remark: {title: '备注',order: 4,view: 'text', type: 'string',},
};
