<template>
    <div class="right">
        <waterInfo v-if="isShowInfo" v-for="(item, i) in waterInfoData" :key="i" :title="item.title" :arrData="item.arrData" :position="item.position" @click="updaSele(item.id, item.to)" :isSelect="nowSelect === item.id" />

        <div class="waterIntakeNetwork" :class="isShow ? 'waterIntakeNetwork_c' : ''" @click="updaSeleWaterIntakeNetwork()">取水管网</div>

        <img class="light light0" src="../../../../assets/images/largeScreen/right/light0.png" />
        <img class="light light1" src="../../../../assets/images/largeScreen/right/light1.png" />
        <img class="light light2" src="../../../../assets/images/largeScreen/right/light2.png" />
        <img class="light light3" src="../../../../assets/images/largeScreen/right/light3.png" />
        <img class="light light4" src="../../../../assets/images/largeScreen/right/light4.png" />

        <echartsBox :titleText="'供水统计'" style="top: 201px; right: 19px;" v-show="!nowSelect && !isShow">
            <div class="echarts0" ref="echarts0"></div>
            <img class="pieBGC" src="../../../../assets/images/largeScreen/right/pieBGC.png" />
        </echartsBox>

        <echartsBox :titleText="'减压池液位'" style="top: 521px; right: 19px;" v-show="!nowSelect && !isShow">
            <div class="echarts1" ref="echarts1"></div>
        </echartsBox>
        
        <infoBox :text="waterPanelInfo.name" style="right: 20px; top: 50%; transform: translateY(-50%);" :isSelect="true" v-if="nowSelect">
            <infoData :title="'基本信息'" :i="0" :data="[
                {
                    name: '编号',
                    data: waterPanelInfo.waterWorksNum
                },
                {
                    name: '联系人',
                    data: waterPanelInfo.contactsName
                },
                {
                    name: '联系电话',
                    data: waterPanelInfo.contactsPhone
                },
                {
                    name: '供水能力',
                    data: waterPanelInfo.supply + waterPanelInfo.supplyUnit
                },
                {
                    name: '服务范围',
                    data: waterPanelInfo.servicesScope
                },
            ]" :row="2" />
            <infoData :title="'地理信息'" :i="1" :data="[
                {
                    name: '经度',
                    data: waterPanelInfo.longitude
                },
                {
                    name: '纬度',
                    data: waterPanelInfo.latitude
                },
                {
                    name: '地址',
                    data: waterPanelInfo.address
                },
            ]" />
            <infoData :title="'工程信息'" :i="2" :data="[
                {
                    name: '水厂简介',
                    data: waterPanelInfo.introduce
                },
                {
                    name: '水源地',
                    data: waterPanelInfo.waterSource
                },
            ]" />
            <router-link class="startJump" :to="readyTo">开始调度</router-link>
        </infoBox>

        <infoBox :text="'取水管网'" style="right: 20px; top: 50%; transform: translateY(-50%);" :isSelect="true" v-if="isShow">
            <infoData :title="'基本信息'" :i="0" :data="waterIntakeNetwork" />
            <router-link class="startJump" :to="readyTo">开始调度</router-link>
        </infoBox>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'
import type { Ref } from 'vue'
import waterInfo from './waterInfo.vue'

import { init } from 'echarts'
import type { EChartsType, EChartsOption } from 'echarts'

import echartsBox from './echartsBox.vue'

import LCG669WebSocket from '../../../../ts/LCG669WebSocket'

import { getWaterPanelInfo, getWaterIntakeNetworkPanelInfo } from '@/api/engineering'

import infoBox from './infoBox.vue'
import infoData from './infoData.vue'

const waterPanelInfo: Ref<WaterPanelInfo> = ref({
    /**
     * 水厂名称
     */
    name: '',
    /**
     * 编号
     */
    waterWorksNum: '',
    /**
     * 联系人
     */
    contactsName: '',
    /**
     * 联系电话
     */
    contactsPhone: '',
    /**
     * 供水能力
     */
    supply: '',
    /**
     * 供水能力 单位
     */
    supplyUnit: '',
    /**
     * 服务范围
     */
    servicesScope: '',
    /**
     * 经度
     */
    longitude: '',
    /**
     * 纬度
     */
    latitude: '',
    /**
     * 地址
     */
    address: '',
    /**
     * 水厂简介
     */
    introduce: '',
    /**
     * 水源地
     */
    waterSource: ''
})

interface WaterPanelInfo {
    /**
     * 水厂名称
     */
    name: string
    /**
     * 编号
     */
    waterWorksNum: string
    /**
     * 联系人
     */
    contactsName: string
    /**
     * 联系电话
     */
    contactsPhone: string
    /**
     * 供水能力
     */
    supply: string
    /**
     * 供水能力 单位
     */
    supplyUnit: string
    /**
     * 服务范围
     */
    servicesScope: string
    /**
     * 经度
     */
    longitude: string
    /**
     * 纬度
     */
    latitude: string
    /**
     * 地址
     */
    address: string
    /**
     * 水厂简介
     */
    introduce: string
    /**
     * 水源地
     */
    waterSource: string
}

const readyTo = ref()

const nowSelect = ref()

const updaSele = (id: string, to: string) => {
    readyTo.value = to

    if (nowSelect.value === id) {
        nowSelect.value = undefined
        isShow.value = false
    } else {
        getWaterPanelInfo({
            id
        })
        .then((e) => {
            isShow.value = false
            waterPanelInfo.value = e
            nowSelect.value = id
        })
    }
}

interface WaterIntakeNetwork {
    name: string
    data: string
    unit: string
}

const waterIntakeNetwork: Ref<WaterIntakeNetwork[]> = ref()

const isShow = ref(false)

const updaSeleWaterIntakeNetwork = () => {
    readyTo.value = '/inspectionCoating/topology'

    getWaterIntakeNetworkPanelInfo()
    .then((e) => {
        nowSelect.value = undefined
        isShow.value = !isShow.value
        waterIntakeNetwork.value = e
    })
}

const echarts0 = ref()
const echarts1 = ref()

/**
 * 饼图 实例0
 */
let Echarts0: EChartsType
/**
 * 面积图 实例1
 */
let Echarts1: EChartsType
/**
 * 饼图
 */
const pie: EChartsOption = {
    graphic: {
        type: 'text',
        left: '9%',
        top: 'center',
        style: {
            text: '',
            // text: total + '\n本月总次数',
            fill: '#595959',
            fontSize: 14,
            // @ts-ignore
            textAlign: 'center',
        }
    },
    legend: {
        icon: 'circle',
        top: '20%',
        right: '55px',
        width: '140px',
        itemWidth: 10,
        itemHeight: 10,
        textStyle: {
            // 文字的样式
            fontSize: 24, // 可控制每个legend项的间距
            color: "#828282",
            rich: {
                // 通过富文本rich给每个项设置样式，下面的oneone、twotwo、threethree可以理解为"每一列"的样式
                oneone: {
                    width: 80,
                    color: "#262626",
                    fontSize: 14,
                },
                threethree: {
                    color: "#262626",
                    fontSize: 14,
                },
            },
        },
    },
    series: [
        {
            name: 'Access From',
            type: 'pie',
            radius: ['40%', '50%'], // 控制饼图的内外半径，从而间接控制饼图的“宽度”  
            center: ['25%', '50%'], // 饼图的中心位置 
            avoidLabelOverlap: false,
            label: {
                show: false,
            },
            labelLine: {
                show: false
            },
            emphasis: {
                scaleSize: 1
            },
            data: [],
        }
    ]
}
/**
 * 面积图 series 数据配置
 */
const areaGraphSeriesItem = {
    type: 'line',
    stack: 'Total',
    areaStyle: {},
    emphasis: {
        focus: 'series'
    },
}
/**
 * 面积图
 */
const areaGraph: EChartsOption = {
    graphic: {
        elements: [{
            type: 'text',
            right: '3%', // 表示文本的左边界与图表容器的右边界对齐
            top: '5%', // 距离容器顶部12%的位置
            style: {
                text: '单位：m', // 文本内容
                fill: '#000', // 文本颜色
                fontSize: 14 // 文本字体大小
            },
        }],
    },
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'cross',
            label: {
                backgroundColor: '#6a7985'
            }
        }
    },
    legend: {
        width: '300px',
        left: '0',
        top: '5%',
        icon: 'circle',
        itemWidth: 10,
        itemHeight: 10,
    },
    grid: {
        top: '33%',
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
    },
    xAxis: {
        type: 'category',
        boundaryGap: false,
        data: []
    },
    yAxis: {
            type: 'value'
    },
    series: []
}

onMounted(() => {
    Echarts0 = init(echarts0.value)
    Echarts1 = init(echarts1.value)

    Echarts0.setOption(pie)
    Echarts1.setOption(areaGraph)
})

const waterInfoData = ref([
    {
        title: '热荣水厂',
        to: '/configuration/reRong/configuration',
        arrData: [
            {
                name: '今日供水量',
                data: '',
                unit: 'm³'
            },
            {
                name: '出水浊度',
                data: '',
                unit: 'NTU'
            },
            {
                name: '出水余氯',
                data: '',
                unit: 'mg/L'
            },
            {
                name: '出水PH',
                data: '',
                unit: undefined
            },
        ],
        position: {
            top: '542px',
            left: '143px'
        },
        id: ''
    },
    {
        title: '日当水厂',
        to: '/configuration/riDang/configuration',
        arrData: [
            {
                name: '今日供水量',
                data: '',
                unit: 'm³'
            },
            {
                name: '出水浊度',
                data: '',
                unit: 'NTU'
            },
            {
                name: '出水余氯',
                data: '',
                unit: 'mg/L'
            },
            {
                name: '出水PH',
                data: '',
                unit: undefined
            },
        ],
        position: {
            top: '198px',
            left: '565px'
        },
        id: ''
    },
    {
        title: '县城水厂',
        to: '/configuration/xiancheng/configuration',
        arrData: [
            {
                name: '今日供水量',
                data: '',
                unit: 'm³'
            },
            {
                name: '出水浊度',
                data: '',
                unit: 'NTU'
            },
            {
                name: '出水余氯',
                data: '',
                unit: 'mg/L'
            },
            {
                name: '出水PH',
                data: '',
                unit: undefined
            },
        ],
        position: {
            top: '205px',
            left: '1000px'
        },
        id: ''
    },
    {
        title: '机场水厂',
        to: '/configuration/jiChang/configuration',
        arrData: [
            {
                name: '今日供水量',
                data: '',
                unit: 'm³'
            },
            {
                name: '出水浊度',
                data: '',
                unit: 'NTU'
            },
            {
                name: '出水余氯',
                data: '',
                unit: 'mg/L'
            },
            {
                name: '出水PH',
                data: '',
                unit: undefined
            },
        ],
        position: {
            top: '790px',
            left: '1080px'
        },
        id: ''
    },
])

interface webSocketData {
    /**
     * 数据框
     */
    waterQuality: WaterQuality[]
    /**
     * 饼图
     */
    waterFta: {
        name: string
        data: string
    }[]
    /**
     * 面积图
     */
    lt: {
        line: {
            name: string
            data: string
        }[]
        time: {
            name: string
            data: string
        }[]
    }
}

const dispatchingCenter: Ref<webSocketData> = ref()

interface WaterQuality {
    id: string
    clo2: {
        data: string
        unit: string
    }
    dayFta: {
        data: string
        unit: string
    }
    ph: {
        data: string
        unit: string
    }
    ss: {
        data: string
        unit: string
    }
}

const isShowInfo = ref(false)

const total = ref(0)
const assemblyData = () => {
    isShowInfo.value = true

    dispatchingCenter.value = lcg669WebSocket.ckData

    waterInfoData.value.forEach((item, i) => {
        item.arrData = [
            {
                ...lcg669WebSocket.ckData.waterQuality[i].dayFta,
                name: '今日供水量'
            },
            {
                ...lcg669WebSocket.ckData.waterQuality[i].ss,
                name: '出水浊度'
            },
            {
                ...lcg669WebSocket.ckData.waterQuality[i].clo2,
                name: '出水余氯'
            },
            {
                ...lcg669WebSocket.ckData.waterQuality[i].ph,
                name: '出水PH'
            },
        ]
        item.id = lcg669WebSocket.ckData.waterQuality[i].id
    })

    // 处理 饼图 数据
    pie.series[0].data.length = 0
    total.value = 0
    lcg669WebSocket.ckData.waterFta.forEach(item => {
        pie.series[0].data.push({
            name: item.name,
            value: item.data
        })
        total.value += Number(item.data)
    })
    // @ts-ignore
    pie.legend.formatter = (name: string) => {
        let target: number

        pie.series[0].data.forEach(e => {
            const item = e as { name: string, value: number }
            if (item.name === name) {
                target = item.value
            }
        })

        var v = ((target / total.value) * 100).toFixed(2);
        return `{oneone|${name}} {threethree|${v}%}`;
        //     富文本第一列样式应用    富文本第二列样式应用      富文本第三列样式应用
    },
    Echarts0.setOption(pie)

    // 处理 面积图 数据
    // @ts-ignore
    areaGraph.xAxis.data.push(
        lcg669WebSocket.ckData.lt.time[0].data
    )
    // 如果没有结构，生成一个结构
    // @ts-ignore
    if (areaGraph.series.length === 0) {
        // 按后端数据 生成基本结构
        lcg669WebSocket.ckData.lt.line.forEach(item => {
            // @ts-ignore
            areaGraph.series.push({
                ...areaGraphSeriesItem,
                name: item.name,
                data: []
            })
        })
    }
    // 按基本机构，填入data数据
    // @ts-ignore
    areaGraph.series.forEach((item, i) => {
        item.data.push(lcg669WebSocket.ckData.lt.line[i].data)
    })
    // @ts-ignore
    areaGraph.series.forEach(item => {
        if (item.data.length > 6) {
            item.data.shift()
        }
    })
    // @ts-ignore
    if (areaGraph.xAxis.data.length > 6) {
        // @ts-ignore
        areaGraph.xAxis.data.shift()
    }
    Echarts1.setOption(areaGraph)
}

const lcg669WebSocket: LCG669WebSocket<webSocketData> = new LCG669WebSocket('screenWebsocket', 'dispatch', assemblyData)

onUnmounted(() => {
    lcg669WebSocket.closeWebSocket()
})
</script>

<style lang="less" scoped>
.right {
    width: 100%;
    height: 100%;
    background-image: url('../../../../assets/images/largeScreen/right/rightBGC.png');
    background-size: 1920px 1080px;
    background-repeat: no-repeat;

    .waterIntakeNetwork {
        transition: all .5s;
        padding: 13px 27px 12px;
        background: #0001F2;
        border-radius: 10px;
        font-weight: 700;
        font-size: 24px;
        color: #FFFFFF;
        line-height: 35px;
        position: absolute;
        top: 705px;
        left: 626px;
        cursor: pointer;
    }
    .waterIntakeNetwork_c {
        background: linear-gradient( 270deg, #0001F2 0%, #79EFE1 50%, #0001F2 100%);
    }

    .light {
        position: absolute;
    }
    .light0 {
        left: 284px;
        top: 385px;
    }
    .light1 {
        left: 810px;
        top: 380px;
    }
    .light2 {
        left: 990px;
        top: 610px;
    }
    .light3 {
        left: 1150px;
        top: 390px;
    }
    .light4 {
        left: 665px;
        top: 540px;
    }

    .echarts0 {
        width: 450px;
        height: 250px;
    }
    .echarts1 {
        width: 450px;
        height: 350px;
    }
    .pieBGC {
        position: absolute;
        top: 100px;
        left: 38px;
    }
    .startJump {
        width: 150px;
        line-height: 50px;
        background: linear-gradient( 180deg, #A1E5FF 0%, #1890FF 50%, #0143FF 100%);
        box-shadow: inset 0 -2px 5px 0 #61CCFF;
        border-radius: 50px;
        font-weight: 700;
        font-size: 18px;
        color: #FFFFFF;
        text-align: center;
        position: relative;
        left: 50%;
        transform: translateX(-50%);
        cursor: pointer;
        margin: 40px 0 20px;
        display: block;
    }
}
</style>