// 导入three.js
import * as THREE from 'three'

/**
 * ClosePage 关闭页面
 */
export default (scene: THREE.Scene, renderer: THREE.WebGLRenderer, resizeFun: Function, tickIdArr: number[]) => {
    if (scene) {
        /**
         * 销毁模型的网格和材质
         */
        scene.traverse(item => {
            if (item.geometry && item.material) {
    
                item.geometry.dispose()
    
                if (item.material.length && item.material.length > 0) {
                    item.material.forEach(m =>{
                        m.dispose()
                    })
                } else {
                    item.material.dispose()
                }
            }
        })

        scene.clear()
    }

    if (renderer) {
        renderer.forceContextLoss() // 强制WebGL上下文丢失  
        renderer.context = null
        renderer.domElement = null
        renderer.dispose()
    }

    window.removeEventListener('resize', resizeFun)

    if (tickIdArr.length > 0) {
        tickIdArr.forEach(item => {
            // 停止每一帧循环渲染
            window.cancelAnimationFrame(item)
        })
    }
}