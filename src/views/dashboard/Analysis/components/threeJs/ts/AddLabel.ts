// 导入three.js
import * as THREE from 'three'

/**
 * png加载器
 */
const loadLabelMap = new THREE.TextureLoader()

/**
 * 设备png图片
 */
const meterPng = loadLabelMap.load('/threejs/img/reRong/index/meter.png')
meterPng.name = 'meter.png'

/**
 * 根据 名称数组 创建 对应模型 和 对应图片
 */
export class InitLabel {
    /**
     * Label标签 3D对象 集合
     */
    labels = new THREE.Group()

    /**
     * 根据 模型名称数组 搜索 对应模型 添加 对应png标签
     * png图片标签放在 public/texture/ 与模型同名
     * @param scene 场景画布
     * @param LabelName 模型名称数组
     */
    constructor(scene: THREE.Scene | THREE.Group, LabelName: string[]) {
        LabelName.forEach(item => {
            /**
             * 搜索模型
             */
            const model = scene.getObjectByName(item)
            model.userData = {
                modelName: item
            }

            /**
             * 加载图片
             */
            const png = loadLabelMap.load('/threejs/img/reRong/index/' + item + '.png')

            /**
             * 模型当前处于的坐标点
             */
            const v3 = new THREE.Vector3()
            model.getWorldPosition(v3)

            /**
             * 创建点精灵的材质
             */
            const labelMaterial = new THREE.SpriteMaterial({
                // 标签对应的贴图，如果需要可以自由控制
                map: png,
                depthTest: false, // 粒子不会被遮挡
                transparent: true
            })
            /**
             * 修正图标颜色
             */
            labelMaterial.map.colorSpace = THREE.SRGBColorSpace
            labelMaterial.sizeAttenuation = false

            const label = new THREE.Sprite(labelMaterial)
            label.userData = {
                /**
                 * 根据模型名称 定义 .userData.modelName 值
                 */
                modelName: item
            }
            label.renderOrder = 100
            
            label.scale.set(150 / 800, 58 / 800, 0)
            label.position.set(
                v3.x,
                v3.y,
                v3.z
            )

            this.labels.add(label)
        })
    }
}

/**
 * 根据 名称数组 统一创建仪表标签
 */
export class InitMeter {
    
    /**
     * Label标签 3D对象 集合
     */
    labels = new THREE.Group()

    constructor(scene: THREE.Scene | THREE.Group, LabelName: string[]) {
        LabelName.forEach(item => {
            /**
             * 搜索模型
             */
            const model = scene.getObjectByName(item)
            model.userData = {
                modelName: item
            }

            /**
             * 模型当前处于的坐标点
             */
            const v3 = new THREE.Vector3()
            model.getWorldPosition(v3)

            /**
             * 创建点精灵的材质
             */
            const labelMaterial = new THREE.SpriteMaterial({
                // 标签对应的贴图，如果需要可以自由控制
                map: meterPng,
                depthTest: false, // 粒子不会被遮挡
                transparent: true
            })
            /**
             * 修正图标颜色
             */
            labelMaterial.map.colorSpace = THREE.SRGBColorSpace
            labelMaterial.sizeAttenuation = false

            const label = new THREE.Sprite(labelMaterial)
            label.userData = {
                /**
                 * 根据模型名称 定义 .userData.modelName 值
                 */
                modelName: item
            }
            label.renderOrder = 100
            
            label.scale.set(30 / 669, 30 / 669, 0)
            label.position.set(
                v3.x,
                v3.y,
                v3.z
            )

            this.labels.add(label)
        })
    }
}