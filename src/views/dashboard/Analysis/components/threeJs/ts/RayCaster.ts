// 射线检测 ！！！只有鼠标左键参与检测

// 导入three.js
import * as THREE from 'three'

// 导入vue
import { ref } from 'vue'

/**
 * 按键检测 单击检测
 */
export class ClickRayCaster {
    /**
     * 射线
     */
    private rayCaster = new THREE.Raycaster()
    /**
     * 点击时鼠标在屏幕的位置 单位像素
     */
    private pointer = new THREE.Vector2()
    /**
     * 用于投射的摄像机
     */
    private camera: THREE.Camera
    /**
     * Label标签 3D对象 集合
     */
    private labels: THREE.Object3D
    /**
     * 当前选中的标签模型
     */
    nowModel: THREE.Object3D
    /**
     * 保存鼠标当前 按下位置
     */
    private clickCanvasClient = {
        clientX: 0,
        clientY: 0
    }
    /**
     * intersect[0].point
     */
    point: THREE.Vector3
    /**
     * 射线检测的回调方法
     */
    private callback: Function
    /**
     * Canvas 元素引用，用于获取正确的尺寸
     */
    private canvasElement: HTMLCanvasElement

    /**
     *
     * @param camera 用于投射的摄像机
     * @param labels 所有标签对象
     * @param callback 射线检测回调函数
     * @param canvasElement Canvas 元素引用（可选，如果不提供则使用 window 尺寸）
     */
    constructor(camera: THREE.Camera, labels: THREE.Object3D, callback: Function, canvasElement?: HTMLCanvasElement) {
        this.camera = camera
        this.labels = labels
        this.callback = callback
        this.canvasElement = canvasElement
    }
    
    /**
     * 鼠标按下时
     */
    down(event: MouseEvent) {
        // 是鼠标左键
        if (event.button === 0) {
            // 保存当前鼠标位置
            this.clickCanvasClient.clientX = event.clientX
            this.clickCanvasClient.clientY = event.clientY
        }
    }

    /**
     * 鼠标抬起时
     * @param event 
     * @param fun 回调事件
     */
    up(event: MouseEvent) {
        // 是鼠标左键 && 比较鼠标位置 按下与抬起相同 才能运行
        if (
            event.button === 0 &&
            this.clickCanvasClient.clientX === event.clientX &&
            this.clickCanvasClient.clientY === event.clientY
        ) {
            this.click(event)

            this.callback()
        }
    }

    /**
     * 单击事件 进行射线检测
     */
    private click(event: MouseEvent) {
        // 获取正确的画布尺寸
        let canvasWidth: number
        let canvasHeight: number

        if (this.canvasElement) {
            // 使用 Canvas 元素的实际渲染尺寸
            const rect = this.canvasElement.getBoundingClientRect()
            canvasWidth = rect.width
            canvasHeight = rect.height

            // 计算相对于 Canvas 的鼠标位置
            const canvasX = event.clientX - rect.left
            const canvasY = event.clientY - rect.top

            this.pointer.x = (canvasX / canvasWidth) * 2 - 1
            this.pointer.y = -(canvasY / canvasHeight) * 2 + 1
        } else {
            // 回退到使用 window 尺寸（兼容旧版本）
            canvasWidth = window.innerWidth
            canvasHeight = window.innerHeight

            this.pointer.x = (event.clientX / canvasWidth) * 2 - 1
            this.pointer.y = -(event.clientY / canvasHeight) * 2 + 1
        }

        this.rayCaster.setFromCamera(this.pointer, this.camera)

        /**
         * 每当射线检测时回调
         * @param intersect 射线检测到的的所有 3D对象 数组
         */
        const intersectObj = (intersect: THREE.Intersection[]) => {
            this.nowModel = undefined

            const isRun = ref(true)

            // 只有添加标签的模型，才能检测，userData.modelName 在创建标签时赋值
            if (intersect.length > 0) {
                intersect.forEach(item => {
                    if (isRun.value && item.object && item.object.userData && item.object.userData.modelName) {
                        isRun.value = false
                      console.log("🚀 ~ item:",item)
                        this.point = item.point
                        this.nowModel = item.object
                    }
                })
            }
        }

        // 检测对象 是否检测所有后代
        intersectObj(this.rayCaster.intersectObject(this.labels, true))
    }
}
