// 全局状态管理
// import { levelPosition } from './PublicData'

// 导入three.js
import * as THREE from 'three'

// import { GUI } from 'three/addons/libs/lil-gui.module.min.js'
// const gui = new GUI( { width: 280 } )

export interface CameraConfigData {
  position: THREE.Vector3
}

/**
 * 相机配置 CameraConfig
 */
export default (camera: THREE.PerspectiveCamera, cameraConfigData: CameraConfigData): void => {
  // 相机坐标
  camera.position.set(
    cameraConfigData.position.x,
    cameraConfigData.position.y,
    cameraConfigData.position.z
  )
  // gui.add(camera.position, 'x').min(-100).max(100).step(.1).name('camera.position.x')
  // gui.add(camera.position, 'y').min(-100).max(100).step(.1).name('camera.position.y')
  // gui.add(camera.position, 'z').min(-100).max(100).step(.1).name('camera.position.z')
}