// 过滤间 filterRoom

<template>
<div class="filterRoom">
    <canvas class="threejs3DCanvas" ref="threejs3DCanvas" @mousedown="clickRayCaster ? clickRayCaster.down($event) : undefined" @mouseup="clickRayCaster ? clickRayCaster.up($event) : undefined"></canvas>

    <img class="backIndex" src="/src/assets/images/largeScreen/center/backIndex.png" @click="jumpScene('index')" />

    <YSXK-DataFrame class="threeJsDataFrame" v-for="(item, i) in aData" :key="i" :title="item.title" :position="item.position" :columnData="item.columnData" v-show="isShow(item.modelName)" />

    <div class="titleName">热荣数字孪生水厂-过滤间</div>

    <!-- 需要保留该测试代码，用以触发DOM更新 -->
    <div class="topCS">{{ topCS }}</div>
</div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as THREE from 'three'
import type { GLTF }  from 'three/examples/jsm/loaders/GLTFLoader.js'
import { GLTFLoader }  from 'three/examples/jsm/loaders/GLTFLoader.js'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'

// 导入分离的模块
import BaseLight from './ts/BaseLight'
import CameraConfig from './ts/CameraConfig'
import type { CameraConfigData } from './ts/CameraConfig'
import ControlsConfig from './ts/ControlsConfig'
import type { ControlsConfigData } from './ts/ControlsConfig'
import RendererConfig from './ts/RendererConfig'
import ClosePage from './ts/ClosePage'
import { ClickRayCaster } from './ts/RayCaster'
import { InitLabel, InitMeter } from './ts/AddLabel'

import { useHomePage } from '/@/store/modules/homePage'

import type { WebSocketData } from '/@/ts/publicData'
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader';

const emit = defineEmits(['jumpScene'])

function jumpScene(modelName: string) {
    emit('jumpScene', modelName)
}

interface IndexProps {
    ckData: WebSocketData
}

const props = defineProps<IndexProps>()

const aData = ref()

watch(() => props.ckData, (newData, oldData) => {
    aData.value = [
        {
            // 标题
            title: '回用水池液位计',
            // 数据框位置
            position: {
                top: '669px',
                left: '669px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '液位', // 标题
                    data: newData.index39.data, // 值
                    unit: newData.index39.unit, // 单位
                    flag: newData.index39.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
            ],
            modelName: '池水'
        },
        {
            // 标题
            title: '2#潜水泵',
            // 数据框位置
            position: {
                top: '669px',
                left: '669px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '状态', // 标题
                    data: newData.index40.data, // 值
                    unit: newData.index40.unit, // 单位
                    flag: newData.index40.warningFlag, // 是否报警
                    type: 'AgitatorState', // 数据类型
                },
            ],
            modelName: '潜污泵'
        },
        {
            // 标题
            title: '1#潜水泵',
            // 数据框位置
            position: {
                top: '669px',
                left: '669px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '状态', // 标题
                    data: newData.index41.data, // 值
                    unit: newData.index41.unit, // 单位
                    flag: newData.index41.warningFlag, // 是否报警
                    type: 'AgitatorState', // 数据类型
                },
            ],
            modelName: '潜污泵001'
        },
        {
            // 标题
            title: '2#污泥泵',
            // 数据框位置
            position: {
                top: '669px',
                left: '669px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '状态', // 标题
                    data: newData.index42.data, // 值
                    unit: newData.index42.unit, // 单位
                    flag: newData.index42.warningFlag, // 是否报警
                    type: 'AgitatorState', // 数据类型
                },
            ],
            modelName: '潜污泵002'
        },
        {
            // 标题
            title: '1#污泥泵',
            // 数据框位置
            position: {
                top: '669px',
                left: '669px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '状态', // 标题
                    data: newData.index43.data, // 值
                    unit: newData.index43.unit, // 单位
                    flag: newData.index43.warningFlag, // 是否报警
                    type: 'AgitatorState', // 数据类型
                },
            ],
            modelName: '潜污泵003'
        },
    ]
})

const isShow = (modelName: string) => {
    if (aData.value && clickRayCaster && clickRayCaster.nowModel) {
        return modelName === clickRayCaster.nowModel.userData.modelName
    } else {
        return false
    }
}

const topNum = ref(0)
const topCS = ref('空')

const topTest = () => {
    topNum.value++
    if(clickRayCaster.nowModel && clickRayCaster.nowModel.userData.modelName) {
        topCS.value = clickRayCaster.nowModel.userData.modelName + topNum.value
    } else {
        topCS.value = '没有' + topNum.value
    }
}

const homePage = useHomePage()

let camera: THREE.PerspectiveCamera

/**
 * GLTF模型加载器
 */
const gltfLoader = new GLTFLoader();

const dracoLoader = new DRACOLoader();
dracoLoader.setDecoderPath('/threejs/libs/draco/');
gltfLoader.setDRACOLoader(dracoLoader);

/**
 * vue3虚拟Dom节点 以ref和变量名进行自动关联
 */
const threejs3DCanvas = ref<HTMLCanvasElement>()

/**
 * 渲染器
 */
let renderer: THREE.WebGLRenderer

/**
 * 轨道控制器 阻尼相机
 */
let controls: OrbitControls

/**
 * 创建画布
 */
const scene = new THREE.Scene()

/**
 * 视口宽高
 */
const sizes = {
    width:  0,
    height: 0
}

const tickIdArr: number[] = []

const isRun = ref(true)

/**
 * 不可交互标签，纯显示，按名称
 */
const labelsOnlyShowArr = [
    '回用水池',
    '无阀滤池'
]
// 可交互标签，按名称，统一设备图标
const meterAll = [
    '池水',
    '潜污泵',
    '潜污泵001',
    '潜污泵002',
    '潜污泵003',
]

/**
 * 可交互标签
 */
const labels: THREE.Group = new THREE.Group()
labels.name = 'labels'
/**
 * 不可交互标签，纯显示
 */
const labelsOnlyShow: THREE.Group = new THREE.Group()
labelsOnlyShow.name = 'labelsOnlyShow'
// 可交互标签，设备组
const meterAllModel: THREE.Object3D[] = []

/**
 * 鼠标左键单击射线检测
 */
let clickRayCaster: ClickRayCaster

/**
 * 生成模型标签，纯显示
 */
const getLabelOnlyShow = (model: THREE.Group) => {
    // 创建标签
    const initLabel = new InitLabel(model, labelsOnlyShowArr)

    labelsOnlyShow.add(initLabel.labels)
}
/**
 * 生成统一设备模型标签，meter.png
 */
const getMeter = (model: THREE.Group) => {
    meterAll.forEach(item => {
        meterAllModel.push(model.getObjectByName(item))
    })

    // 创建标签
    const initMeter = new InitMeter(model, meterAll)

    labels.add(initMeter.labels)
}

/**
 * 将生成的模型标签，添加进入场景中
 */
const addLabel = () => {
    scene.add(labels)
    scene.add(labelsOnlyShow)
}

const resizeFun = () => {
    // 更新分辨率
    sizes.width =  threejs3DCanvas.value.offsetWidth,
    sizes.height = threejs3DCanvas.value.offsetHeight

    // 更新摄像机
    camera.aspect = sizes.width / sizes.height
    camera.updateProjectionMatrix()

    renderer.setSize(sizes.width, sizes.height)
}

const world = (tickBackFun: Function) => {
    const controlsConfigData: ControlsConfigData = {
        target: new THREE.Vector3(0, 5, 0),
        minDistance: 0.1,
        maxDistance: 20,
    }
    controls = ControlsConfig(camera, threejs3DCanvas.value, controlsConfigData)

    // 挂载
    renderer = new THREE.WebGLRenderer({
        canvas: threejs3DCanvas.value,
        powerPreference: 'high-performance',
        antialias: true, // 抗锯齿 如果 renderer 要使用必须在这里打开，后处理模式下无效
        alpha: false
    })

    // render 配置
    RendererConfig(sizes.width, sizes.height, renderer)

    // ！！！注意不要随意更改模块运行顺序
    BaseLight(scene, renderer)
    const cameraConfigData: CameraConfigData = {
        position: new THREE.Vector3(3.6160878105482896, 12.073306923644449, -0.0626420435373153)
    }
    CameraConfig(camera, cameraConfigData)

    // 响应式更新
    window.addEventListener('resize', () => {
        // 更新分辨率
        sizes.width = window.innerWidth,
        sizes.height = window.innerHeight

        // 更新摄像机
        camera.aspect = sizes.width / sizes.height
        camera.updateProjectionMatrix()

        renderer.setSize(sizes.width, sizes.height)
    })

    /**
     * 打开阴影的投射与接收
     */
    const openShadow = (model: THREE.Group) => {
        model.traverse((child) => {
            if (child.name !== '科技围墙') {
                child.castShadow = true
                child.receiveShadow = true
            }
        })
    }
    /**
     * 自发光贴图
     */
    const openLight = (model: THREE.Group) => {
        // @ts-ignore
        const scienceAndTechnologyFence = model.getObjectByName('科技围墙') as THREE.Mesh<THREE.BufferGeometry<THREE.NormalBufferAttributes>, THREE.MeshStandardMaterial>
        // scienceAndTechnologyFence.material.lightMap = scienceAndTechnologyFence.material.map
        // scienceAndTechnologyFence.material.lightMapIntensity = .669
        scienceAndTechnologyFence.material.emissive = new THREE.Color(0xFFFFFF)
        scienceAndTechnologyFence.material.emissiveIntensity = 2
        scienceAndTechnologyFence.material.metalness = 0
        scienceAndTechnologyFence.material.roughness = 1

        // const renderScene = (model: THREE.Mesh<THREE.BufferGeometry<THREE.NormalBufferAttributes>, THREE.MeshStandardMaterial>) => {
        //     // 使用requestAnimationFrame函数进行渲染
        //     model.material.map.offset.x -= .0075

        //     tickIdArr[1] = window.requestAnimationFrame(() => {
        //         renderScene(model)
        //     })
        // }
        // renderScene(scienceAndTechnologyFence)
    }

    /**
     * 每帧回调处理
     */
    const tick = () => {
        // 更新阻尼相机轨道动画
        controls.update()

        // 更新FPS显示器
        // stats.update()

        // 更新摄像机坐标
        camera.updateProjectionMatrix()

        renderer.render(scene, camera)

        // 下一帧执行
        tickIdArr[0] = window.requestAnimationFrame(tick)

        // console.log(camera.position)

        tickBackFun()
    }

    const initModel = (gltf: GLTF) => {
        getLabelOnlyShow(gltf.scene)
        getMeter(gltf.scene)
        addLabel()

        // 开始鼠标单击射线检测
        clickRayCaster = new ClickRayCaster(camera, labels,
        // 单击回调事件
        () => {
            topTest()
        },
        // 传入 canvas 元素引用
        threejs3DCanvas.value)

        if (isRun.value) {
            scene.add(gltf.scene.clone())

            // 响应式更新
            window.addEventListener('resize', resizeFun)

            // 开始渲染
            tick()
        }
    }

    if (homePage.filterRoomGltf) {
        initModel(homePage.filterRoomGltf)
    } else {
        /**
         * 导入模型
         */
        gltfLoader.load(
            '/threejs/models/filterRoom-v1.glb',
            (gltf) => {
                openShadow(gltf.scene)
                openLight(gltf.scene)

                homePage.setFilterRoomModel(gltf)

                initModel(homePage.filterRoomGltf)
            }
        )
    }
}

/**
 * 计算 监测点 div 的位置
 */
const calculateData = () => {
    if (props.ckData && aData.value && meterAllModel) {
        const w = sizes.width  / 2
        const h = sizes.height / 2

        meterAllModel.forEach((item, i) => {
            const objP3D = new THREE.Vector3()

            item.getWorldPosition(objP3D)
            /**
             * 仅有 top left 的值
             */
            const objP2D = objP3D.project(camera)

            aData.value.forEach(aDataItem => {
                if (aDataItem.modelName === item.userData.modelName) {
                    aDataItem.position.top  = Math.round(-objP2D.y * h + h) + 'px'
                    aDataItem.position.left = Math.round( objP2D.x * w + w) + 'px'
                }
            })
        })
    }
}

onMounted(() => {
    // 更新分辨率
    sizes.width =  threejs3DCanvas.value.offsetWidth
    sizes.height = threejs3DCanvas.value.offsetHeight

    camera = new THREE.PerspectiveCamera(75, sizes.width / sizes.height, 0.1, 1000)

    world(calculateData)
})

onUnmounted(() => {
    isRun.value = false
    ClosePage(scene, renderer, resizeFun, tickIdArr)
})
</script>

<style lang="less" scoped>
.filterRoom {
    width: 100%;
    height: 100%;
    position: relative;
    .threejs3DCanvas {
        width: 100%;
        height: 100%;
    }
}
</style>
