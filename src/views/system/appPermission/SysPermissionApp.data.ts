import { BasicColumn, FormSchema } from '/@/components/Table';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '模块标题',
    align: 'center',
    dataIndex: 'name',
  },
  {
    title: '英文名',
    align: 'center',
    dataIndex: 'enName',
  },
  {
    title: '路径',
    align: 'center',
    dataIndex: 'url',
  },
  {
    title: '菜单排序',
    align: 'center',
    dataIndex: 'sortNo',
  },
  {
    title: '菜单图标',
    align: 'center',
    dataIndex: 'icon',
  },
  {
    title: '背景色',
    align: 'center',
    dataIndex: 'background',
  },
  {
    title: '描述',
    align: 'center',
    dataIndex: 'description',
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '父模块',
    field: 'pName',
    component: 'Input',
    dynamicDisabled: true,
    //ifShow和show属性一致，使用其中一个即可，values代表当前表单的值，js 控制，会删除 dom
    ifShow: ({ values }) => {
      return typeof values.pName !== 'undefined';
    },
  },
  {
    label: '模块标题',
    field: 'name',
    component: 'Input',
    required: true,
  },
  {
    label: '英文名',
    field: 'enName',
    component: 'Input',
  },
  {
    label: '路径',
    field: 'url',
    component: 'Input',
    required: true,
  },
  {
    label: '菜单排序',
    field: 'sortNo',
    component: 'InputNumber',
  },
  {
    label: '菜单图标',
    field: 'icon',
    component: 'Input',
  },
  {
    label: '背景色',
    field: 'background',
    component: 'Input',
  },
  {
    label: '描述',
    field: 'description',
    component: 'Input',
  },
  {
    label: '备注',
    field: 'remark',
    component: 'Input',
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

// 高级查询数据
export const superQuerySchema = {
  parentId: { title: '父Id', order: 0, view: 'text', type: 'string' },
  name: { title: '模块标题', order: 1, view: 'text', type: 'string' },
  enName: { title: '英文名', order: 2, view: 'text', type: 'string' },
  url: { title: '路径', order: 3, view: 'text', type: 'string' },
  sortNo: { title: '菜单排序', order: 4, view: 'number', type: 'number' },
  icon: { title: '菜单图标', order: 5, view: 'text', type: 'string' },
  background: { title: '背景色', order: 6, view: 'text', type: 'string' },
  description: { title: '描述', order: 7, view: 'text', type: 'string' },
  remark: { title: '备注', order: 8, view: 'text', type: 'string' },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
