<template>
    <div class="MonitoringPoint">
        <div class="MonitoringPointMenu">
            <a-select v-model:value="select" style="width: 240px;" @change="handleChange" show-search :filter-option="filterOption" placeholder="请选择监测点">
                <a-select-option v-for="(item, i) in selectOption" :key="i" :value="item.id">{{ item.name }}</a-select-option>
            </a-select>

            <div class="MonitoringPointMenuInfo" style="margin-left: 12px;">
                <div class="MonitoringPointMenuName">
                    编号：
                </div>
                <div class="MonitoringPointMenuData">
                    {{ info.pointCode }}
                </div>
            </div>
            <div class="MonitoringPointMenuInfo">
                <div class="MonitoringPointMenuName">
                    类型：
                </div>
                <div class="MonitoringPointMenuData">
                    {{ info.typeName }}
                </div>
            </div>
            <div class="MonitoringPointMenuInfo">
                <div class="MonitoringPointMenuName">
                    管径：
                </div>
                <div class="MonitoringPointMenuData">
                    {{ info.diam }}
                </div>
            </div>
            <div class="MonitoringPointMenuInfo">
                <div class="MonitoringPointMenuName">
                    桩号：
                </div>
                <div class="MonitoringPointMenuData">
                    {{ info.pileNo }}
                </div>
            </div>

            <div class="MonitoringPointMenuButton">
                <a-button type="link" preIcon="ant-design:info-circle-outlined" @click="toA">监测点信息</a-button>
                <a-button type="link" preIcon="ant-design:line-chart-outlined"  @click="toB">数据查询</a-button>
            </div>
        </div>

        <!-- 监测点-液位
        压力&阀门&液位 -->
        <div class="MonitoringPointContent" v-if=     "nowType === 'PT_VALVE_LT' && isShow">
            <div class="MonitoringPointContentDataFrame DataFrame0">
                <YSXK-TimeUpda
                    :position  ="timeUpda.position"
                    :recentTime="timeUpda.recentTime"
                />
                <YSXK-DataFrame
                    v-for="(item, i) in aData"
                    :key="i"
                    :position="item.position"
                    :columnData="item.columnData"
                />
            </div>

            <div class="MonitoringPointContentControl">
                <div class="ControlTitle">
                    阀门控制
                </div>

                <div class="ControlValue">

                    <div class="ControlItem" style="text-align: center;">
                        <div class="ControlData">
                            <a-popconfirm title="确认修改？" ok-text="是" cancel-text="否" @confirm="confirmFun('PAR_F', 'valveId')">
                                <a-slider       v-model:value="controlsParameter.valve" :min="0" :max="100" class="slider" :step="0.01" :marks="marks" />
                                <a-input-number v-model:value="controlsParameter.valve" :min="0" :max="100" class="num"    :step="0.01" :controls="false" />
                                <div class="unit">%</div>
                            </a-popconfirm>
                        </div>
                    </div>

                    <div class="ControlItem">
                        <div class="model" style="float: left; margin-left: 6%;">
                            <div class="modelTitle">当前控制模式：</div>
                            <div class="voluntarily" v-if     ="controlsParameter.auto === '1'">自动</div>
                            <div class="manual"      v-else-if="controlsParameter.auto === '0'">手动</div>
                        </div>
                        <div class="model" style="float: right; margin-right: 6%;">
                            <div class="modelTitle" style="margin-right: 13px;">设置：</div>
                            <a-popconfirm title="确认修改？" ok-text="是" cancel-text="否" @confirm="confirmFun('AUTO', 'autoId')">
                                <a-button type="primary" class="button" :disabled="controlsParameter.auto === '0'">手动</a-button>
                            </a-popconfirm>
                            <a-popconfirm title="确认修改？" ok-text="是" cancel-text="否" @confirm="confirmFun('AUTO', 'autoId')">
                                <a-button type="primary" class="button" :disabled="controlsParameter.auto === '1'" style="margin-left: 15px;">自动</a-button>
                            </a-popconfirm>
                        </div>
                    </div>

                    <div class="ControlItem">
                        <div class="ControlData">
                            <div class="ControlSet">
                                <div>
                                    <div class="title" style="margin-right: 12px;">{{ controlsParameter.maxZhName }}</div>
                                    <div class="title">当前值：</div>
                                    <div class="data">{{ controlsParameter.max + controlsParameter.maxUnit }}</div>
                                </div>
                                <div style="margin-top: 12px;">
                                    <div class="title" style="margin-right: 12px;">设定值</div>
                                    <a-input-number class="input" v-model:value="controlsParameter.newMax" placeholder="请输入" step="10" />
                                    <div class="unit" style="margin-right: 12px;">{{ controlsParameter.maxUnit }}</div>
                                    <a-popconfirm title="确认修改？" ok-text="是" cancel-text="否" @confirm="confirmFun('PAR_F', 'maxId')">
                                        <a-button class="button" type="primary">确定</a-button>
                                    </a-popconfirm>
                                </div>

                                <div>
                                    <div class="title" style="margin-right: 12px;">{{ controlsParameter.middleZhName }}</div>
                                    <div class="title">当前值：</div>
                                    <div class="data">{{ controlsParameter.middle + controlsParameter.middleUnit }}</div>
                                </div>
                                <div style="margin-top: 12px;">
                                    <div class="title" style="margin-right: 12px;">设定值</div>
                                    <a-input-number class="input" v-model:value="controlsParameter.newMiddle" placeholder="请输入" step="10" />
                                    <div class="unit" style="margin-right: 12px;">{{ controlsParameter.middleUnit }}</div>
                                    <a-popconfirm title="确认修改？" ok-text="是" cancel-text="否" @confirm="confirmFun('PAR_F', 'middleId')">
                                        <a-button class="button" type="primary">确定</a-button>
                                    </a-popconfirm>
                                </div>
                                
                                <div>
                                    <div class="title" style="margin-right: 12px;">{{ controlsParameter.minZhName }}</div>
                                    <div class="title">当前值：</div>
                                    <div class="data">{{ controlsParameter.min + controlsParameter.minUnit }}</div>
                                </div>
                                <div style="margin-top: 12px;">
                                    <div class="title" style="margin-right: 12px;">设定值</div>
                                    <a-input-number class="input" v-model:value="controlsParameter.newMin" placeholder="请输入" step="10" />
                                    <div class="unit" style="margin-right: 12px;">{{ controlsParameter.minUnit }}</div>
                                    <a-popconfirm title="确认修改？" ok-text="是" cancel-text="否" @confirm="confirmFun('PAR_F', 'minId')">
                                        <a-button class="button" type="primary">确定</a-button>
                                    </a-popconfirm>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="controlBGC" v-show="isControlBGC" @click="controlMessage"></div>
                </div>
            </div>
        </div>

        <!-- 监测点-压力
        压力 -->
        <div class="MonitoringPointContent" v-else-if="nowType === 'PT' && isShow">
            <div class="MonitoringPointContentDataFrame DataFrame1">
                <YSXK-TimeUpda
                    :position  ="timeUpda.position"
                    :recentTime="timeUpda.recentTime"
                />
                <YSXK-DataFrame
                    v-for="(item, i) in aData"
                    :key="i"
                    :position="item.position"
                    :columnData="item.columnData"
                />
            </div>
        </div>

        <!-- 监测点-压力&阀门
        压力&阀门 -->
        <div class="MonitoringPointContent" v-else-if="nowType === 'PT_VALVE' && isShow">
            <div class="MonitoringPointContentDataFrame DataFrame2">
                <YSXK-TimeUpda
                    :position  ="timeUpda.position"
                    :recentTime="timeUpda.recentTime"
                />
                <YSXK-DataFrame
                    v-for="(item, i) in aData"
                    :key="i"
                    :position="item.position"
                    :columnData="item.columnData"
                />
            </div>

            <div class="MonitoringPointContentControl">
                <div class="ControlTitle">
                    阀门控制
                </div>
                
                <div class="ControlValue" style="background-color: #E7F3F7;">
                    <div class="control">
                        <a-popconfirm title="确认打开？" ok-text="是" cancel-text="否" @confirm="confirmFun('OPEN', 'openId')">
                            <a-button class="greenButton" style="width: 120px; margin: 0 2.5%;">打开</a-button>
                        </a-popconfirm>
                        <a-popconfirm title="确认关闭？" ok-text="是" cancel-text="否" @confirm="confirmFun('CLOSE', 'closeId')">
                            <a-button class="redButton"   style="width: 120px; margin: 0 2.5%;">关闭</a-button>
                        </a-popconfirm>
                        <a-popconfirm title="确认停止？" ok-text="是" cancel-text="否" @confirm="confirmFun('STOP', 'stopId')">
                            <a-button class="blueButton"  style="width: 120px; margin: 0 2.5%;">停止</a-button>
                        </a-popconfirm>
                        <a-popconfirm title="确认复位？" ok-text="是" cancel-text="否" @confirm="confirmFun('RESET_FAULT', 'faultResetId')">
                            <a-button class="blueButton"  style="width: 120px; margin: 0 2.5%;">复位</a-button>
                        </a-popconfirm>
                    </div>

                    <div class="controlBGC" v-show="isControlBGC" @click="controlMessage"></div>
                </div>
            </div>
        </div>
        
        <!-- 监测点-压力&阀门&流量
        压力&阀门&流量 -->
        <div class="MonitoringPointContent" v-else-if="nowType === 'PT_VALVE_FT' && isShow">
            <div class="MonitoringPointContentDataFrame DataFrame3">
                <YSXK-TimeUpda
                    :position  ="timeUpda.position"
                    :recentTime="timeUpda.recentTime"
                />
                <YSXK-DataFrame
                    v-for="(item, i) in aData"
                    :key="i"
                    :position="item.position"
                    :columnData="item.columnData"
                />
            </div>

            <div class="MonitoringPointContentControl">
                <div class="ControlTitle">
                    阀门控制
                </div>
                
                <div class="ControlValue" style="background-color: #E7F3F7;">
                    <div class="control">
                        <a-popconfirm title="确认打开？" ok-text="是" cancel-text="否" @confirm="confirmFun('OPEN', 'openId')">
                            <a-button class="greenButton" style="width: 120px; margin: 0 2.5%;">打开</a-button>
                        </a-popconfirm>
                        <a-popconfirm title="确认关闭？" ok-text="是" cancel-text="否" @confirm="confirmFun('CLOSE', 'closeId')">
                            <a-button class="redButton"   style="width: 120px; margin: 0 2.5%;">关闭</a-button>
                        </a-popconfirm>
                        <a-popconfirm title="确认停止？" ok-text="是" cancel-text="否" @confirm="confirmFun('STOP', 'stopId')">
                            <a-button class="blueButton"  style="width: 120px; margin: 0 2.5%;">停止</a-button>
                        </a-popconfirm>
                        <a-popconfirm title="确认复位？" ok-text="是" cancel-text="否" @confirm="confirmFun('RESET_FAULT', 'faultResetId')">
                            <a-button class="blueButton"  style="width: 120px; margin: 0 2.5%;">复位</a-button>
                        </a-popconfirm>
                    </div>
                    
                    <div class="controlBGC" v-show="isControlBGC" @click="controlMessage"></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import type { Ref, Reactive } from 'vue'
import { getMonitoringPointSelectList, getMonitoringPointData, postSetMonitoringPoint } from '/@/api/engineering'
import { useRoute, useRouter } from 'vue-router'
import FlowRegulatingValve_gray from '../../assets/images/monitoringPoint/FlowRegulatingValve_gray.png'
import FlowRegulatingValve_green from '../../assets/images/monitoringPoint/FlowRegulatingValve_green.png'
import FlowRegulatingValve_orange from '../../assets/images/monitoringPoint/FlowRegulatingValve_orange.png'
import electricValve_gray from '../../assets/images/monitoringPoint/electricValve_gray.png'
import electricValve_green from '../../assets/images/monitoringPoint/electricValve_green.png'
import electricValve_orange from '../../assets/images/monitoringPoint/electricValve_orange.png'
import { message } from 'ant-design-vue'

const controlMessage = () => {
    message.error('就地状态无法进行远程控制！')
}
 
const route = useRoute()

const router = useRouter()

const isControlBGC = ref(false)

onMounted(() => {
    const id = route.query.id as string | undefined

    getMonitoringPointSelectList()
    .then(e => {
        selectOption.value = e

        if (id) {
            select.value = id
        } else {
            select.value = selectOption.value[0].id
        }
        handleChange(select.value)
    })
})

const toA = () => {
    router.push('/engineering/relationMonitorList')
}
const toB = () => {
    router.push('/monitoring/history')
}

export interface Confirm {
    opType: 'START' | 'STOP' | 'OPEN' | 'CLOSE' | 'RESET_FAULT' | 'CLEAR_TIMES' | 'PAR_F' | 'UNKNOWN' | 'AUTO' | 'OPEN_CLOSE'
    value?: string
    varId : string
}

const confirm: Reactive<Confirm> = reactive({
    opType: undefined,
    value : undefined,
    varId : undefined,
})

/**
 * 气泡确认框 是
 */
const confirmFun = (opType: Confirm['opType'], idName: string) => {
    confirm.opType = opType

    if (idName === 'valveId') {
        confirm.value = String(controlsParameter.valve)
    } else if (idName === 'autoId') {
        confirm.value = undefined
    } else if (idName === 'maxId') {
        confirm.value = String(controlsParameter.newMax)
    } else if (idName === 'middleId') {
        confirm.value = String(controlsParameter.newMiddle)
    } else if (idName === 'minId') {
        confirm.value = String(controlsParameter.newMin)
    } else {
        confirm.value = undefined
    }

    confirm.varId = ids[idName]

    postSetMonitoringPoint(confirm)
    .then(e => {
        
    })
}

const controlsParameter = reactive({
    valve: 0,
    auto: '',

    max: 0,
    newMax: 0,
    maxUnit: '',
    maxZhName: '',

    middle: 0,
    newMiddle: 0,
    middleUnit: '',
    middleZhName: '',
    
    min: 0,
    newMin: 0,
    minUnit: '',
    minZhName: '',
})

const filterOption = (input: string, option: { value: string }) => {
    let yes = false

    selectOption.value.forEach(item => {
        /**
         * 找到id相同的
         */
        if (item.id === option.value) {
            /**
             * 是否包含字符串
             */
            yes = item.name.includes(input)
        }
    })

    return yes
}

const timeUpda = reactive({
    position: {
        top: '24px',
        left: '24px'
    },
    recentTime: ''
})

// 假设这是水泵数据
const aData = ref([])

const marks = ref<Record<number, any>>({
    0: '0%',
    25: '25%',
    50: '50%',
    75: '75%',
    100: '100%',
})

const info: Reactive<Info> = reactive({
    pointCode: '',
    typeName: '',
    diam: '',
    pileNo: '',

    collectTime: '',
})

interface SelectOption {
    id: string
    name: string
    /**
     * UI类型 切换
     */
    type: string
}

const selectOption: Ref<SelectOption[]> = ref([])

const select = ref()

const nowType = ref('')

interface Info {
    pointCode: string
    typeName: string
    diam: string
    pileNo: string

    collectTime: string
}

interface DataFrame {
    valve: {
        data: string
        unit: string
    }
    valveSet: {
        id: string
    }
    status: {
        data: string
    }
    remote: {
        data: string
    }
    auto: {
        data: string
        id: string
    }
    pt: {
        data: string
        unit: string
    }
    lt: {
        data: string
        unit: string
    }
    ft: {
        data: string
        unit: string
    }
    fta: {
        data: string
        unit: string
    }
    /**
     * parSet[0]最大值 parSet[1]最小值
     */
    parSet: {
        data: string
        unit: string
        zhName: string
        id: string
    }[]
    open: {
        id: string
    }
    close: {
        id: string
    }
    stop: {
        id: string
    }
    faultReset: {
        id: string
    }
}

interface Ids {
    valveId: string
    autoId: string
    maxId: string
    middleId: string
    minId: string

    openId: string,
    closeId: string,
    stopId: string,
    faultResetId: string,
}

const ids: Reactive<Ids> = reactive({
    valveId: undefined,
    autoId: undefined,
    maxId: undefined,
    middleId: undefined,
    minId: undefined,

    openId: undefined,
    closeId: undefined,
    stopId: undefined,
    faultResetId: undefined,
})

const getInfo = (res: Info) => {
    info.pointCode = res.pointCode
    info.typeName  = res.typeName
    info.diam      = res.diam
    info.pileNo    = res.pileNo

    timeUpda.recentTime = res.collectTime
}

const getIds0 = (dataFrame: DataFrame) => {
    ids.valveId  = dataFrame.valveSet.id
    ids.autoId   = dataFrame.auto.id
    ids.maxId    = dataFrame.parSet[0].id
    ids.middleId = dataFrame.parSet[1].id
    ids.minId    = dataFrame.parSet[2].id
}

const getIds1 = (dataFrame: DataFrame) => {
    ids.openId = dataFrame.open.id
    ids.closeId = dataFrame.close.id
    ids.stopId = dataFrame.stop.id
    ids.faultResetId = dataFrame.faultReset.id
}

const isShow = ref(false)

const nowId = ref('')

const handleChange = (id: string, ones?: boolean) => {
    nowId.value = id

    selectOption.value.forEach(item => {
        if (item.id === id) {
            nowType.value = item.type
            switch (nowType.value) {
                // 监测点-液位
                // 压力&阀门&液位
                case 'PT_VALVE_LT':
                    getMonitoringPointData({
                        pointId: id
                    })
                    .then(e => {
                        const _info = e as Info
                        
                        getInfo(_info)
                        
                        const dataFrame = e as DataFrame

                        getIds0(dataFrame)

                        if (ones === true) {
                            controlsParameter.auto = dataFrame.auto.data
                            controlsParameter.max    = Number(dataFrame.parSet[0].data)
                            controlsParameter.middle = Number(dataFrame.parSet[1].data)
                            controlsParameter.min    = Number(dataFrame.parSet[2].data)
                        } else {
                            controlsParameter.valve = Number(dataFrame.valve.data)
                            controlsParameter.auto = dataFrame.auto.data
    
                            controlsParameter.max = Number(dataFrame.parSet[0].data)
                            controlsParameter.newMax = controlsParameter.max
                            controlsParameter.maxUnit   = dataFrame.parSet[0].unit
                            controlsParameter.maxZhName = dataFrame.parSet[0].zhName
    
                            controlsParameter.middle = Number(dataFrame.parSet[1].data)
                            controlsParameter.newMiddle = controlsParameter.middle
                            controlsParameter.middleUnit   = dataFrame.parSet[1].unit
                            controlsParameter.middleZhName = dataFrame.parSet[1].zhName
    
                            controlsParameter.min = Number(dataFrame.parSet[2].data)
                            controlsParameter.newMin = controlsParameter.min
                            controlsParameter.minUnit   = dataFrame.parSet[2].unit
                            controlsParameter.minZhName = dataFrame.parSet[2].zhName
                        }

                        if (dataFrame.remote.data === '0') {
                            isControlBGC.value = true
                        } else {
                            isControlBGC.value = false
                        }

                        aData.value = [
                            {
                                // 数据框位置
                                position: {
                                    top: '83px',
                                    left: '292px'
                                },
                                // 每一条数据
                                columnData: [
                                    {
                                        name: '开度', // 标题
                                        data: dataFrame.valve.data, // 值
                                        unit: dataFrame.valve.unit, // 单位
                                        flag: '0', // 是否报警
                                        type: '', // 数据类型
                                    },
                                    {
                                        name: '状态', // 标题
                                        data: dataFrame.status.data, // 值
                                        type: 'ValveState', // 数据类型
                                        img  : FlowRegulatingValve_green,
                                        imgX : FlowRegulatingValve_orange,
                                        imgX2: FlowRegulatingValve_gray,
                                        // 图片位置
                                        location: {
                                            top: '188px',
                                            left: '322px'
                                        },
                                        // 图片宽高
                                        size: {
                                            w: '120px',
                                            h: '113px'
                                        }
                                    },
                                    {
                                        name: '控制模式', // 标题
                                        data : dataFrame.remote.data, // 值
                                        data2: dataFrame.auto.data, // 值
                                        type: 'AgitatorMode', // 数据类型
                                    },
                                ]
                            },
                            {
                                // 数据框位置
                                position: {
                                    top: '83px',
                                    left: '684px'
                                },
                                // 每一条数据
                                columnData: [
                                    {
                                        name: '压力', // 标题
                                        data: dataFrame.pt.data, // 值
                                        unit: dataFrame.pt.unit, // 单位
                                        flag: '0', // 是否报警
                                        type: '', // 数据类型
                                    },
                                ],
                            },
                            {
                                // 数据框位置
                                position: {
                                    top: '83px',
                                    left: '1137px'
                                },
                                // 每一条数据
                                columnData: [
                                    {
                                        name: '液位', // 标题
                                        data: dataFrame.lt.data, // 值
                                        unit: dataFrame.lt.unit, // 单位
                                        flag: '0', // 是否报警
                                        type: '', // 数据类型
                                    },
                                ],
                            }
                        ]
                        
                        isShow.value = true
                    })
                break
                // 监测点-压力
                // 压力
                case 'PT':
                    getMonitoringPointData({
                        pointId: id
                    })
                    .then(e => {
                        const _info = e as Info
                        
                        getInfo(_info)

                        const dataFrame = e as DataFrame

                        aData.value = [
                            {
                                // 数据框位置
                                position: {
                                    top: '282px',
                                    left: '871px'
                                },
                                // 每一条数据
                                columnData: [
                                    {
                                        name: '压力', // 标题
                                        data: dataFrame.pt as unknown as string === '' ? '-' : dataFrame.pt.data, // 值
                                        unit: dataFrame.pt as unknown as string === '' ? '-' : dataFrame.pt.unit, // 单位
                                        flag: '0', // 是否报警
                                        type: '', // 数据类型
                                    },
                                ],
                            }
                        ]
                        
                        isShow.value = true
                    })
                break
                // 监测点-压力&阀门
                // 压力&阀门
                case 'PT_VALVE':
                    getMonitoringPointData({
                        pointId: id
                    })
                    .then(e => {
                        const _info = e as Info
                        
                        getInfo(_info)

                        const dataFrame = e as DataFrame

                        getIds1(dataFrame)

                        if (dataFrame.remote.data === '0') {
                            isControlBGC.value = true
                        } else {
                            isControlBGC.value = false
                        }

                        aData.value = [
                            {
                                // 数据框位置
                                position: {
                                    top: '79px',
                                    left: '487px'
                                },
                                // 每一条数据
                                columnData: [
                                    {
                                        name: '开度', // 标题
                                        data: dataFrame.valve.data, // 值
                                        unit: dataFrame.valve.unit, // 单位
                                        flag: '0', // 是否报警
                                        type: '', // 数据类型
                                    },
                                    {
                                        name: '状态', // 标题
                                        data: dataFrame.status.data, // 值
                                        type: 'ValveState', // 数据类型
                                        img  : electricValve_green,
                                        imgX : electricValve_orange,
                                        imgX2: electricValve_gray,
                                        // 图片位置
                                        location: {
                                            top: '208px',
                                            left: '504px'
                                        },
                                        // 图片宽高
                                        size: {
                                            w: '129px',
                                            h: '160px'
                                        }
                                    },
                                    {
                                        name: '控制模式', // 标题
                                        data : dataFrame.remote.data, // 值
                                        data2: dataFrame.auto.data, // 值
                                        type: 'AgitatorMode', // 数据类型
                                    },
                                ],
                            },
                            {
                                // 数据框位置
                                position: {
                                    top: '93px',
                                    left: '1043px'
                                },
                                // 每一条数据
                                columnData: [
                                    {
                                        name: '压力', // 标题
                                        data: dataFrame.pt.data, // 值
                                        unit: dataFrame.pt.unit, // 单位
                                        flag: '0', // 是否报警
                                        type: '', // 数据类型
                                    },
                                ],
                            },
                        ]
                        
                        isShow.value = true
                    })
                break
                // 监测点-压力&阀门&流量
                // 压力&阀门&流量
                case 'PT_VALVE_FT':
                    getMonitoringPointData({
                        pointId: id
                    })
                    .then(e => {
                        const _info = e as Info
                        
                        getInfo(_info)

                        const dataFrame = e as DataFrame

                        getIds1(dataFrame)

                        if (dataFrame.remote.data === '0') {
                            isControlBGC.value = true
                        } else {
                            isControlBGC.value = false
                        }

                        aData.value = [
                            {
                                // 数据框位置
                                position: {
                                    top: '79px',
                                    left: '362px'
                                },
                                // 每一条数据
                                columnData: [
                                    {
                                        name: '开度', // 标题
                                        data: dataFrame.valve.data, // 值
                                        unit: dataFrame.valve.unit, // 单位
                                        flag: '0', // 是否报警
                                        type: '', // 数据类型
                                    },
                                    {
                                        name: '状态', // 标题
                                        data: dataFrame.status.data, // 值
                                        type: 'ValveState', // 数据类型
                                        img  : electricValve_green,
                                        imgX : electricValve_orange,
                                        imgX2: electricValve_gray,
                                        // 图片位置
                                        location: {
                                            top: '208px',
                                            left: '379px'
                                        },
                                        // 图片宽高
                                        size: {
                                            w: '129px',
                                            h: '160px'
                                        }
                                    },
                                    {
                                        name: '控制模式', // 标题
                                        data : dataFrame.remote.data, // 值
                                        data2: dataFrame.auto.data, // 值
                                        type: 'AgitatorMode', // 数据类型
                                    },
                                ],
                            },
                            {
                                // 数据框位置
                                position: {
                                    top: '93px',
                                    left: '726px'
                                },
                                // 每一条数据
                                columnData: [
                                    {
                                        name: '压力', // 标题
                                        data: dataFrame.pt.data, // 值
                                        unit: dataFrame.pt.unit, // 单位
                                        flag: '0', // 是否报警
                                        type: '', // 数据类型
                                    },
                                ],
                            },
                            {
                                // 数据框位置
                                position: {
                                    top: '93px',
                                    left: '1111px'
                                },
                                // 每一条数据
                                columnData: [
                                    {
                                        name: '瞬时流量', // 标题
                                        data: dataFrame.ft.data, // 值
                                        unit: dataFrame.ft.unit, // 单位
                                        flag: '0', // 是否报警
                                        type: '', // 数据类型
                                    },
                                    {
                                        name: '累计流量', // 标题
                                        data: dataFrame.fta.data, // 值
                                        unit: dataFrame.fta.unit, // 单位
                                        flag: '0', // 是否报警
                                        type: '', // 数据类型
                                    },
                                ],
                            },
                        ]
                        
                        isShow.value = true
                    })
                break
            }
        }
    })
}

/**
 * 多少秒刷新一次数据
 */
const timeGet = 5 * 1000

const timer = setInterval(() => {
    if (nowId.value !== '') {
        handleChange(nowId.value, true)
    }
}, timeGet)

onUnmounted(() => {
    clearInterval(timer)
})
</script>

<style lang="less" scoped>
.MonitoringPoint {
    width: 100%;
    height: 100%;
    background-color: #F5F5F5;
    padding: 8px;
    padding-bottom: 0;
    .MonitoringPointMenu {
        background-color: white;
        padding: 16px 12px;
        position: relative;
        margin-bottom: 12px;
        .MonitoringPointMenuInfo {
            display: inline-block;
            font-weight: 400;
            font-size: 14px;
            line-height: 32px;
            margin-right: 24px;
            .MonitoringPointMenuName {
                display: inline-block;
                color: #9E9E9E;
            }
            .MonitoringPointMenuData {
                display: inline-block;
                color: #595959;
            }
        }
        .MonitoringPointMenuButton {
            position: absolute;
            top: 50%;
            right: 12px;
            transform: translateY(-50%);
            background-color: white;
        }
    }
    .MonitoringPointContent {
        width: 100%;
        height: calc(100% - 76px);
        background-color: #F5F5F5;
        .MonitoringPointContentDataFrame {
            width: 1688px;
            background-repeat: no-repeat;
            position: relative;
            margin-bottom: 12px;
            transform: rotate(360deg);
        }
        .DataFrame0 {
            background-image: url('../../assets/images/monitoringPoint/DataFrameBGC0.png');
            height: 446px;
            background-size: 1688px 446px;
        }
        .DataFrame1 {
            background-image: url('../../assets/images/monitoringPoint/DataFrameBGC1.png');
            height: calc(820px - 60px);
            background-size: 1688px calc(820px - 60px);
            margin-bottom: 0;
        }
        .DataFrame2 {
            background-image: url('../../assets/images/monitoringPoint/DataFrameBGC2.png');
            height: 446px;
            background-size: 1688px 446px;
        }
        .DataFrame3 {
            background-image: url('../../assets/images/monitoringPoint/DataFrameBGC3.png');
            height: 446px;
            background-size: 1688px 446px;
        }
        .MonitoringPointContentControl {
            width: 100%;
            height: 300px;
            background-color: white;
            position: relative;
            overflow: hidden;
            .ControlTitle {
                font-weight: 700;
                font-size: 16px;
                color: #262626;
                padding: 12px;
                border-bottom: 1px solid #F0F0F0;
            }
            .ControlValue {
                padding: 12px 0;
                height: 272px;
                position: relative;
                box-sizing: border-box;
                .ControlItem:not(:last-child) {
                    border-right: 1px solid #F0F0F0;
                }
                .ControlItem {
                    width: calc(100% / 3);
                    height: 100%;
                    display: inline-block;
                    position: relative;
                    // padding: 48px;
                    .ControlData {
                        width: 100%;
                        position: absolute;
                        top: 50%;
                        transform: translateY(-50%);
                        box-sizing: border-box;
                        .slider {
                            display: inline-block;
                            vertical-align: middle;
                            // width: 64.15876776871097%;
                            width: 60%;
                        }
                        .num {
                            margin-left: 24px;
                            // margin-left: 16px;
                            display: inline-block;
                            vertical-align: middle;
                            width: 64px;
                            min-width: 64px;
                        }
                        .data {
                            font-weight: 700;
                            font-size: 16px;
                            display: inline-block;
                            color: #1890FF;
                        }
                        .title {
                            font-weight: 400;
                            font-size: 16px;
                            color: #262626;
                            display: inline-block;
                        }
                        .input {
                            display: inline-block;
                            width: 120px;
                            line-height: 30px;
                            border-radius: 8px;
                            input {
                                padding: 0;
                            }
                        }
                        .ControlSet {
                            display: inline-block;
                            position: relative;
                            left: 50%;
                            transform: translateX(-50%);
                        }
                    }
                    .model {
                        line-height: 40px;
                        font-weight: 400;
                        color: #262626;
                        display: inline-block;
                        // vertical-align: bottom;
                        position: relative;
                        top: 50%;
                        transform: translateY(-50%);
                        background-color: white;
                        .modelTitle {
                            font-weight: 400;
                            font-size: 16px;
                            color: #262626;
                            display: inline-block;
                        }
                        .voluntarily, .manual {
                            font-weight: 700;
                            font-size: 16px;
                            display: inline-block;
                        }
                        .voluntarily {
                            color: #F3B23C;
                        }
                        .manual {
                            color: #1890FF;
                        }
                    }
                    .button {
                        padding: 0 12px !important;
                        height: 40px;
                        border-radius: 8px;
                        font-weight: 400;
                        font-size: 18px;
                    }
                    .unit {
                        margin-left: 12px;
                        display: inline-block;
                        font-weight: 400;
                        font-size: 16px;
                        color: #8C8C8C;
                    }
                }
                .control {
                    text-align: center;
                    position: relative;
                    top: 50%;
                    transform: translateY(-50%);
                }
                .controlBGC {
                    background-color: rgba(0, 0, 0, 0.2);
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    top: 0;
                    left: 0;
                    cursor: not-allowed;
                }
            }
        }
    }
}
</style>

<style lang="less">
.MonitoringPoint {
    .ant-input-number-input {
        padding-right: 5px !important;
    }
}
</style>