<template>
    <div class="nonLiquidLevel">
        <div class="MonitoringPointContent" v-if="nowTab.type === '2'">
            <div class="MonitoringPointContentDataFrame DataFrame1">
                <YSXK-TimeUpda
                    :position  ="timeUpda.position"
                    :recentTime="timeUpda.recentTime"
                />
                <YSXK-DataFrame
                    v-for="(item, i) in aData"
                    :key="i"
                    :position="item.position"
                    :columnData="item.columnData"
                />
            </div>

            <div class="MonitoringPointContentControl">
                <div class="ControlTitle">
                    阀门控制
                </div>

                <div class="ControlValue">
                    <div class="ControlItem" style="width: 100%; text-align: center;">
                        <div style="position: relative;">
                            <div class="buttonTitle">安全泄压阀：</div>
                            <div class="buttonBox">
                                <a-popconfirm title="确认打开？" ok-text="是" cancel-text="否" @confirm="confirmFun('OPEN', lcg669WebSocket.ckData.index13.id)">
                                    <a-button class="greenButton" style="width: 120px; margin: 6px;">打开</a-button>
                                </a-popconfirm>
                                <a-popconfirm title="确认关闭？" ok-text="是" cancel-text="否" @confirm="confirmFun('CLOSE', lcg669WebSocket.ckData.index14.id)">
                                    <a-button class="redButton"   style="width: 120px; margin: 6px;">关闭</a-button>
                                </a-popconfirm>
                                <a-popconfirm title="确认停止？" ok-text="是" cancel-text="否" @confirm="confirmFun('STOP', lcg669WebSocket.ckData.index15.id)">
                                    <a-button class="blueButton"  style="width: 120px; margin: 6px;">停止</a-button>
                                </a-popconfirm>
                                <a-popconfirm title="确认复位？" ok-text="是" cancel-text="否" @confirm="confirmFun('RESET_FAULT', lcg669WebSocket.ckData.index16.id)">
                                    <a-button class="blueButton"  style="width: 120px; margin: 6px;">复位</a-button>
                                </a-popconfirm>
                            </div>

                            <div class="controlBGC" v-show="isSafety" @click="controlMessage"></div>
                        </div>
                        
                        <div style="position: relative;">
                            <div class="buttonTitle">出水检修阀：</div>
                            <div class="buttonBox">
                                <a-popconfirm title="确认打开？" ok-text="是" cancel-text="否" @confirm="confirmFun('OPEN', lcg669WebSocket.ckData.index17.id)">
                                    <a-button class="greenButton" style="width: 120px; margin: 6px;">打开</a-button>
                                </a-popconfirm>
                                <a-popconfirm title="确认关闭？" ok-text="是" cancel-text="否" @confirm="confirmFun('CLOSE', lcg669WebSocket.ckData.index18.id)">
                                    <a-button class="redButton"   style="width: 120px; margin: 6px;">关闭</a-button>
                                </a-popconfirm>
                                <a-popconfirm title="确认停止？" ok-text="是" cancel-text="否" @confirm="confirmFun('STOP', lcg669WebSocket.ckData.index19.id)">
                                    <a-button class="blueButton"  style="width: 120px; margin: 6px;">停止</a-button>
                                </a-popconfirm>
                                <a-popconfirm title="确认复位？" ok-text="是" cancel-text="否" @confirm="confirmFun('RESET_FAULT', lcg669WebSocket.ckData.index20.id)">
                                    <a-button class="blueButton"  style="width: 120px; margin: 6px;">复位</a-button>
                                </a-popconfirm>
                            </div>

                            <div class="controlBGC" v-show="isOut" @click="controlMessage"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { postSetMonitoringPoint } from '/@/api/engineering'
import electricValve_gray   from '/@/assets/images/monitoringPoint/electricValve_gray.png'
import electricValve_green  from '/@/assets/images/monitoringPoint/electricValve_green.png'
import electricValve_orange from '/@/assets/images/monitoringPoint/electricValve_orange.png'
import LCG669WebSocket from '/@/ts/LCG669WebSocket'
import type { WebSocketData } from './ordinary.vue'
import { message } from 'ant-design-vue'
import type { Confirm } from '../MonitoringPoint.vue'
import type { TabData } from './ordinary.vue'

const controlMessage = () => {
    message.error('就地状态无法进行远程控制！')
}

/**
 * 安全泄压阀 不允许控制
 */
const isSafety = ref(true)
/**
 * 出水检修阀 不允许控制
 */
const isOut = ref(true)

interface OrdinaryData {
    nowTab: TabData
}

const props = defineProps<OrdinaryData>()

let lcg669WebSocket: LCG669WebSocket<WebSocketData>

const assemblyData = () => {
    console.log('自动获取数据：', lcg669WebSocket.ckData)
    
    timeUpda.recentTime = lcg669WebSocket.ckData.recentTime

    if (lcg669WebSocket.ckData.index7.data === '0') {
        isSafety.value = true
    } else {
        isSafety.value = false
    }

    if (lcg669WebSocket.ckData.index11.data === '0') {
        isOut.value = true
    } else {
        isOut.value = false
    }

    aData.value = [
        {
            // 数据框位置
            position: {
                top: '80px',
                left: '300px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '开度', // 标题
                    data: lcg669WebSocket.ckData.index5.data, // 值
                    unit: lcg669WebSocket.ckData.index5.unit, // 单位
                    flag: lcg669WebSocket.ckData.index5.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
                {
                    name: '状态', // 标题
                    data: lcg669WebSocket.ckData.index6.data, // 值
                    type: 'ValveState', // 数据类型
                    img: electricValve_green,
                    imgX: electricValve_orange,
                    imgX2: electricValve_gray,
                    // 图片位置
                    location: {
                        top: '292px',
                        left: '305px'
                    },
                    // 图片宽高
                    size: {
                        w: '64px',
                        h: '80px'
                    }
                },
                {
                    name: '控制模式', // 标题
                    data: lcg669WebSocket.ckData.index7.data, // 值
                    type: 'AgitatorMode', // 数据类型
                },
                {
                    name: '压力', // 标题
                    data: lcg669WebSocket.ckData.index8.data, // 值
                    unit: lcg669WebSocket.ckData.index8.unit, // 单位
                    flag: lcg669WebSocket.ckData.index8.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
                {
                    name: '瞬时流量', // 标题
                    data: lcg669WebSocket.ckData.index31.data, // 值
                    unit: lcg669WebSocket.ckData.index31.unit, // 单位
                    flag: lcg669WebSocket.ckData.index31.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
                {
                    name: '累计流量', // 标题
                    data: lcg669WebSocket.ckData.index32.data, // 值
                    unit: lcg669WebSocket.ckData.index32.unit, // 单位
                    flag: lcg669WebSocket.ckData.index32.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
            ],
        },
        {
            // 数据框位置
            position: {
                top: '80px',
                left: '1230px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '开度', // 标题
                    data: lcg669WebSocket.ckData.index9.data, // 值
                    unit: lcg669WebSocket.ckData.index9.unit, // 单位
                    flag: lcg669WebSocket.ckData.index9.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
                {
                    name: '状态', // 标题
                    data: lcg669WebSocket.ckData.index10.data, // 值
                    type: 'ValveState', // 数据类型
                    img: electricValve_green,
                    imgX: electricValve_orange,
                    imgX2: electricValve_gray,
                    // 图片位置
                    location: {
                        top: '292px',
                        left: '1244px'
                    },
                    // 图片宽高
                    size: {
                        w: '64px',
                        h: '80px'
                    }
                },
                {
                    name: '控制模式', // 标题
                    data: lcg669WebSocket.ckData.index11.data, // 值
                    type: 'AgitatorMode', // 数据类型
                },
                {
                    name: '压力', // 标题
                    data: lcg669WebSocket.ckData.index12.data, // 值
                    unit: lcg669WebSocket.ckData.index12.unit, // 单位
                    flag: lcg669WebSocket.ckData.index12.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
                {
                    name: '瞬时流量', // 标题
                    data: lcg669WebSocket.ckData.index3.data, // 值
                    unit: lcg669WebSocket.ckData.index3.unit, // 单位
                    flag: lcg669WebSocket.ckData.index3.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
                {
                    name: '累计流量', // 标题
                    data: lcg669WebSocket.ckData.index4.data, // 值
                    unit: lcg669WebSocket.ckData.index4.unit, // 单位
                    flag: lcg669WebSocket.ckData.index4.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
            ],
        },
    ]
}

onMounted(() => {
    lcg669WebSocket = new LCG669WebSocket('ztWebsocket', props.nowTab.key + '/web', assemblyData)
})

onUnmounted(() => {
    lcg669WebSocket.closeWebSocket()
})

/**
 * 气泡确认框 是
 */
const confirmFun = (opType: Confirm['opType'], varId: string, value?: string) => {
    postSetMonitoringPoint({
        opType,
        varId
    })
    .then(e => {

    })
}

const timeUpda = reactive({
    position: {
        top: '24px',
        left: '24px'
    },
    recentTime: ''
})

// 假设这是水泵数据
const aData = ref([])
</script>

<style lang="less" scoped>
.nonLiquidLevel {
    .MonitoringPointContent {
        width: 100%;
        height: calc(100% - 76px);
        background-color: #F5F5F5;
        .MonitoringPointContentDataFrame {
            width: 1688px;
            background-repeat: no-repeat;
            position: relative;
            margin-bottom: 12px;
            transform: rotate(360deg);
        }
        .DataFrame1 {
            background-image: url('../../../assets/images/inspectionCoating/DataFrameBGC1.png');
            height: 446px;
            background-size: 1688px 446px;
        }
        .MonitoringPointContentControl {
            width: 100%;
            height: 320px;
            background-color: white;
            .ControlTitle {
                font-weight: 700;
                font-size: 16px;
                color: #262626;
                padding: 12px;
                border-bottom: 1px solid #F0F0F0;
            }
            .ControlValue {
                padding: 12px 0 30px;
                height: 272px;
                position: relative;
                display: flex;
                .ControlItem:not(:last-child) {
                    border-right: 1px solid #F0F0F0;
                }
                .ControlItem {
                    width: 100%;
                    height: 100%;
                    display: inline-block;
                    position: relative;
                    overflow-y: auto;
                    padding-top: 32px;
                    .buttonTitle {
                        font-weight: 400;
                        font-size: 16px;
                        color: #262626;
                        display: inline-block;
                    }
                    .buttonBox {
                        display: inline-block;
                    }
                    .slider {
                        display: inline-block;
                        vertical-align: middle;
                        // width: 64.15876776871097%;
                        width: 60%;
                    }
                    .num {
                        margin-left: 24px;
                        // margin-left: 16px;
                        display: inline-block;
                        vertical-align: middle;
                        width: 64px;
                        min-width: 64px;
                    }
                    .data {
                        font-weight: 700;
                        font-size: 16px;
                        display: inline-block;
                        color: #1890FF;
                    }
                    .title {
                        font-weight: 400;
                        font-size: 16px;
                        color: #262626;
                        display: inline-block;
                    }
                    .input {
                        display: inline-block;
                        width: 120px;
                        line-height: 30px;
                        border-radius: 8px;
                        input {
                            padding: 0;
                        }
                    }
                    .ControlSet {
                        display: inline-block;
                        position: relative;
                        left: 50%;
                        transform: translateX(-50%);
                    }
                    .model {
                        line-height: 40px;
                        font-weight: 400;
                        color: #262626;
                        display: inline-block;
                        // vertical-align: bottom;
                        position: relative;
                        top: 50%;
                        transform: translateY(-50%);
                        background-color: white;
                        .modelTitle {
                            font-weight: 400;
                            font-size: 16px;
                            color: #262626;
                            display: inline-block;
                        }
                        .voluntarily, .manual {
                            font-weight: 700;
                            font-size: 16px;
                            display: inline-block;
                        }
                        .voluntarily {
                            color: #F3B23C;
                        }
                        .manual {
                            color: #1890FF;
                        }
                    }
                    .button {
                        padding: 0 12px !important;
                        height: 40px;
                        border-radius: 8px;
                        font-weight: 400;
                        font-size: 18px;
                    }
                    .unit {
                        margin-left: 12px;
                        display: inline-block;
                        font-weight: 400;
                        font-size: 16px;
                        color: #8C8C8C;
                    }
                }
                .control {
                    text-align: center;
                    position: relative;
                    top: 50%;
                    transform: translateY(-50%);
                }
                .controlBGC {
                    background-color: rgba(0, 0, 0, 0.2);
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    top: 0;
                    left: 0;
                    cursor: not-allowed;
                }
            }
        }
    }
}
</style>