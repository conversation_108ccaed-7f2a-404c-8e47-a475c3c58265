// 全局状态管理
// import { levelPosition } from './PublicData'

// 导入three.js
import * as THREE from 'three'

// import { GUI } from 'three/addons/libs/lil-gui.module.min.js'
// const gui = new GUI( { width: 280 } )

/**
 * 相机配置 CameraConfig
 */
export default (camera: THREE.PerspectiveCamera): void => {
  // 相机坐标
  camera.position.set(
    -37.02312691383876,
    92.19761659765132,
    35.526670279568926
  )
  // gui.add(camera.position, 'x').min(-100).max(100).step(.1).name('camera.position.x')
  // gui.add(camera.position, 'y').min(-100).max(100).step(.1).name('camera.position.y')
  // gui.add(camera.position, 'z').min(-100).max(100).step(.1).name('camera.position.z')
}