
// 导入three.js
import * as THREE from 'three'

/**
 * 渲染器配置 RendererConfig
 */
export default (width: number, height: number, renderer: THREE.WebGLRenderer) => {
  // 开发环境debug，生产环境关闭可以提升性能
  // renderer.debug.checkShaderErrors = false

  // 设置宽高
  renderer.setSize(width, height)
  // 设置像素比率
  renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2))
  // 开启实时渲染阴影 开启阴影
  renderer.shadowMap.enabled = true
  // 正确光照模式
  // renderer.useLegacyLights = false
  // srgb色彩模式 除非需要使用线性颜色空间进行后期处理，否则在使用glTF的时候将WebGLRenderer进行如下配置
  renderer.outputColorSpace = THREE.SRGBColorSpace
  // 色调映射的曝光级别 默认1 设置色彩空间
  // renderer.toneMapping = THREE.ACESFilmicToneMapping
  renderer.toneMapping = THREE.ReinhardToneMapping
  // PCF 阴影，适合低分辨率 与 shadow.radius 阴影模糊 不兼容
  renderer.shadowMap.type = THREE.PCFSoftShadowMap
}