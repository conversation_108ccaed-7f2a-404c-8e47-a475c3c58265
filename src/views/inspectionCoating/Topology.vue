<template>
    <div class="Topology">
        <div class="e0" ref="chart0"></div>

        <img class="legend" src="/src/assets/images/inspectionCoating/legend.png" />
    </div>
</template>

<script lang="ts" setup>
import { getTopology } from '/@/api/engineering'
import { ref, onMounted } from 'vue'
import { option, Pressure, PressureValves, PressureValvesFlow, PressureValvesLiquidLevel } from './topologyData'

import { init } from 'echarts'
import type { ECharts } from 'echarts'

import { useRouter } from 'vue-router'

const router = useRouter()

const jumpID = (id: string) => {
    router.push('/inspectionCoating/monitoringPoint?id=' + id)
}

const chart0 = ref()

let myChart0: ECharts

onMounted(() => {
    getTopology()
    .then(e => {
        option.series[0].data.forEach((item, i) => {
            e.series[0].data[i].x = item.x
            e.series[0].data[i].y = item.y

            item.id        = e.series[0].data[i].id
            item.latitude  = e.series[0].data[i].latitude
            item.longitude = e.series[0].data[i].longitude
            item.monitorNo = e.series[0].data[i].monitorNo
            item.type      = e.series[0].data[i].type
            item.typeName  = e.series[0].data[i].typeName

            item.symbolSize = [5, 5]
            switch(item.typeName) {
                case '压力': 
                    item.symbol = 'image://' + Pressure
                break
                case '压力&阀门': 
                    item.symbol = 'image://' + PressureValves
                break
                case '压力&阀门&流量': 
                    item.symbol = 'image://' + PressureValvesFlow
                break
                case '压力&阀门&液位': 
                    item.symbol = 'image://' + PressureValvesLiquidLevel
                break
            }
        })

        option.series[0].links = e.series[0].links

        option.series[0].data[0].typeName = e.series[0].data[0].typeName
        
        console.log('拓扑数据：!!!!!!!!!!!', option)

        myChart0 = init(chart0.value)
        myChart0.setOption(option)

        myChart0.on('click', params => {
            // @ts-ignore
            if (params.data.id) {
                // @ts-ignore
                jumpID(params.data.id)
            }
        })
    })
})
</script>

<style lang="less" scoped>
.Topology {
    width: 100%;
    height: 100%;
    position: relative;
    .e0 {
        width: 100%;
        height: 100%;
        position: absolute;
    }
    .legend {
        width: 319px;
        height: 295px;
        position: absolute;
        left: 0;
        bottom: 0;
        user-select: none;
    }
}
</style>