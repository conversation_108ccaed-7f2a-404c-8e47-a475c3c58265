// 多路播放 Multiplexing

<template>
    <div class="Multiplexing">
        <div class="MultiplexingVideoBox">
            <div class="MultiplexingVideoItem" :class="'videoNum' + videoData.urls.length" v-for="(item, i) in videoData.urls">
                <!-- <video class="video-js vjs-default-skin MultiplexingVideo" controls"></video> -->
            </div>
        </div>

        <div class="MultiplexingTitle">
            <span>播放时间：</span>
            <a-select v-model:value="expireTime" style="width: 100px; margin-left: 8px" placeholder="请选择播放时间">
                <a-select-option :value="item.value" v-for="(item, i) in selectVideoListTime">{{ item.label }}</a-select-option>
            </a-select>
            <span style="padding-left: 16px;">视频清晰度：</span>
            <a-select v-model:value="quality" style="width: 100px; margin-left: 8px">
                <a-select-option :value="item.value" v-for="item in selectVideoQuality">{{ item.label }}</a-select-option>
            </a-select>
            <span style="padding-left: 16px;">摄像头：</span>
            <a-select v-model:value="selectVideo" style="width: 1000px;" placeholder="请选择多个摄像头" mode="multiple" show-search :filter-option="filterOption">
                <a-select-option :value="item.value" v-for="(item, i) in selectVideoList">{{ item.label }}</a-select-option>
            </a-select>
            <a-button type="primary" style="margin-left: 8px" @click="action" :icon="h(PlayCircleOutlined)" :loading="isLoading">开始播放</a-button>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import type { Ref, Reactive } from 'vue'
import videojs from 'video.js'
import 'video.js/dist/video-js.min.css'
import { postVideoToken, getVideoList } from '/@/api/engineering'
import axios from 'axios'
import { message } from 'ant-design-vue'
import { h } from 'vue'
import { PlayCircleOutlined } from '@ant-design/icons-vue'
import { zh_cn } from '/@/ts/publicData'

videojs.addLanguage('zh-cn', zh_cn)

/**
 * 多路播放 节流防抖
 */
const isLoading = ref(false)

const options = {
    language: 'zh-cn',
}

const filterOption = (input: string, option: any) => {
    return selectVideoList.value.find(item => item.value === option.value && item.label.includes(input))
}

/**
 * 视频清晰度
 */
const quality = ref(2)
const selectVideoQuality = [
    {
        label: '流畅',
        value: 2
    },
    {
        label: '高清',
        value: 1
    },
]
/**
 * 最大多路播放数量
 * 推荐：3 * 3 = 9，4 * 4 = 16，5 * 5 = 25
 * 以此类推
 * 需要补充下面的css样式
 */
const maxVideo = 3 * 3

interface VideoData {
    /**
     * 所有地址
     */
    urls: string[],
    /**
     * 所有视频播放器实例
     */
    player: any[],
}

const videoData: Reactive<VideoData> = reactive({
    /**
     * 所有地址
     */
    urls: [],
    /**
     * 所有视频播放器实例
     */
    player: [],
})

const selectVideoListTime = [
    {
        label: '30秒',
        value: 30
    },
    {
        label: '5分钟',
        value: 5 * 60
    },
    {
        label: '30分钟',
        value: 30 * 60
    },
    {
        label: '1小时',
        value: 60 * 60
    },
    {
        label: '12小时',
        value: 60 * 60 * 12
    },
]
/**
 * 当前选中的时间
 */
const expireTime = ref(selectVideoListTime[1].value)
/**
 * 当前选中的摄像头
 */
const selectVideo: Ref<string[]> = ref([])

interface SelectVideoList {
    value: string,
    label: string,
}

/**
 * 下拉框数据
 */
const selectVideoList: Ref<SelectVideoList[]> = ref([])
/**
 * 萤石云 accessToken
 */
let accessToken: string
/**
 * 请求头
 */
const headers = {
    'Content-Type': 'application/x-www-form-urlencoded',
}

// 创建一个新的<video>元素  
const videoElement = document.createElement('video')
// 设置这个<video>元素的id和其他属性
videoElement.className = 'video-js vjs-default-skin MultiplexingVideo'
videoElement.controls = true
videoElement.autoplay = true
videoElement.muted = true

const action = () => {
    // value 的格式为 "L02937691_3" 需要按下划线分割，只取前面的字符串
    // console.log('selectVideo.value', selectVideo.value)
    // console.log('value', value)
    if (selectVideo.value.length >= 1 && selectVideo.value.length <= maxVideo) {
        isLoading.value = true

        videoData.urls.length = 0
    
        const promiseArr = []
    
        selectVideo.value.forEach(item => {
            const arr = item.split('_')
            promiseArr.push(fetchVideoSrc(arr[0], Number(arr[1])))
        })
    
        playerDispose()
    
        Promise.all(promiseArr)
        .then(AllData => {
            console.log('所有数据请求成功AllData：', AllData)
            AllData.forEach(e => {
                // console.log('url', e.data.data)
                if (e.data.code === '200') {
                    videoData.urls.push(e.data.data.url)
                } else {
                    videoData.urls.push(undefined)
                }
            })
    
            nextTick(() => {
                const newFatherDom = document.querySelectorAll('.MultiplexingVideoItem')
                // console.log('newDom', newFatherDom)
    
                newFatherDom.forEach((item, i) => {
                    const clone = videoElement.cloneNode(true)
                    item.appendChild(clone)
    
                    // 初始化 视频播放器
                    const player = videojs(clone as Element, options)
                    // 设置视频链接
                    if (videoData.urls[i]) {
                        player.src({
                            src: videoData.urls[i], // 直播流地址
                            type: 'application/x-mpegURL' // HLS 流的 MIME 类型
                        })
                    } else {
                        player.src({
                            src: 'https://minio.yunsxk.com:9099/xizang/video/equipmentOffline.mp4', // 设备离线视频
                            type: 'video/mp4' // mp4 视频格式
                        })

                        message.error(selectVideoList.value[i].label + ' 设备离线！')
                    }

                    videoData.player.push(player)
                })
    
                clearTimer()
                timer = setTimeout(() => {
                    playerDispose()
                    message.success('播放结束')
                }, expireTime.value * 1000)

                isLoading.value = false
            })
        })
    } else {
        if (selectVideo.value.length < 1) {
            message.error('请先选择摄像头！')
        } else {
            message.error('最多选择' + maxVideo + '个摄像头！')
            selectVideo.value.pop()
        }
    }
}
/**
 * 释放所有m3u8资源，删除video dom元素 
 */
const playerDispose = () =>  {
    videoData.player.forEach(item => {
        item.dispose()
    })
    videoData.player.length = 0
}

onMounted(() => {
    // 获取 token
    postVideoToken()
    .then(e => {
        // console.log('返回的token：', e)

        accessToken = e
    })

    // 获取 萤石云 视频列表
    getVideoList()
    .then(e => {
        // console.log('返回的列表：', e)

        selectVideoList.value.length = 0

        e.forEach(item => {
            selectVideoList.value.push({
                value: item.key,
                label: item.name,
            })
        })
    })
})

/**
 * 萤石云 获取播放地址
 * @param deviceSerial 设备序列号例如427734222，均采用英文符号，限制最多50个字符
 */
const fetchVideoSrc = (deviceSerial: string, channelNo: number) => {
    return axios({
        method: 'post',
        url: 'https://open.ys7.com/api/lapp/v2/live/address/get',
        data: {
            accessToken,
            protocol: 2,
            expireTime: expireTime.value,
            quality: quality.value,
            deviceSerial,
            channelNo
        },
        headers
    })
}

/**
 * 计时器
 */
let timer: NodeJS.Timeout

/**
 * 清除计时器
 */
const clearTimer = () => {
    if (timer) {
        clearTimeout(timer)
    }
}

onUnmounted(() => {
    clearTimer()
    playerDispose()
})
</script>

<style lang="less">
.Multiplexing {
    width: 100%;
    height: 100%;
    .MultiplexingTitle {
        padding: 8px;
        background-color: white;
    }
    .MultiplexingVideoBox {
        // background-color: gray;
        background-color: black;
        width: 100%;
        height: calc(100% - 48px);
        .MultiplexingVideoItem {
            display: inline-block;
            .MultiplexingVideo {
                width: 100%;
                height: 100%;
            }
        }
        // 1 * 1 = 1
        .videoNum1 {
            width:  calc(100% / 1);
            height: calc(100% / 1);
        }
        // 2 * 2 = 4
        .videoNum2, .videoNum3, .videoNum4 {
            width:  calc(100% / 2);
            height: calc(100% / 2);
        }
        // 3 * 3 = 9
        .videoNum5, .videoNum6, .videoNum7, .videoNum8, .videoNum9 {
            width:  calc(100% / 3);
            height: calc(100% / 3);
        }
        // 4 * 4 = 16
        .videoNum10, .videoNum11, .videoNum12, .videoNum13, .videoNum14, .videoNum15, .videoNum16 {
            width:  calc(100% / 4);
            height: calc(100% / 4);
        }
        // 5 * 5 = 25
        .videoNum17, .videoNum18, .videoNum19, .videoNum20, .videoNum21, .videoNum22, .videoNum23, .videoNum24, .videoNum25 {
            width:  calc(100% / 5);
            height: calc(100% / 5);
        }
        // 6 * 6 = 36
        .videoNum26, .videoNum27, .videoNum28, .videoNum29, .videoNum30, .videoNum31, .videoNum32, .videoNum33, .videoNum34, .videoNum35, .videoNum36 {
            width:  calc(100% / 6);
            height: calc(100% / 6);
        }
    }
}
</style>