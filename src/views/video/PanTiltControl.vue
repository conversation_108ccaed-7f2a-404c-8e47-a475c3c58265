// 云台控制 PanTiltControl

<template>
    <div class="PanTiltControl">
        <div id="videoElementBox" class="PanTiltControlVideoBox">
            <!-- <video id="videoElement" class="video-js vjs-default-skin PanTiltControlVideo" controls></video> -->
        </div>

        <div class="PanTiltControlTitle">
            <span>播放时间：</span>
            <a-select v-model:value="expireTime" style="width: 100px; margin-left: 8px">
                <a-select-option :value="item.value" v-for="item in selectVideoListTime">{{ item.label }}</a-select-option>
            </a-select>
            <span style="padding-left: 16px;">视频清晰度：</span>
            <a-select v-model:value="quality" style="width: 100px; margin-left: 8px">
                <a-select-option :value="item.value" v-for="item in selectVideoQuality">{{ item.label }}</a-select-option>
            </a-select>
            <span style="padding-left: 16px;">摄像头：</span>
            <a-select v-model:value="selectVideo" style="width: 300px;" placeholder="请选择1个摄像头" show-search :filter-option="filterOption">
                <a-select-option :value="item.value" v-for="item in selectVideoList">{{ item.label }}</a-select-option>
            </a-select>
            <a-button type="primary" style="margin-left: 8px" @click="action" :icon="h(PlayCircleOutlined)">开始播放</a-button>
        </div>

        <div class="PanTiltControlPanel" v-if="src && src !== 'https://minio.yunsxk.com:9099/xizang/video/equipmentOffline.mp4'">
            <div class="PanTiltControlPanelDirection">
                <!-- 0-上 -->
                <img class="PanTiltControlPanelDirectionIcon" @mousedown="handleMouseDown(0)" @mouseup="handleMouseUp()" @mouseleave="handleMouseLeave()" ondragstart="return false;" :src="'/src/assets/direction/top'         + (direction === 0 ? '_c' : '') + '.png'" style="top: 7px; left: 95px;" />
                <!-- 6-右上 -->
                <img class="PanTiltControlPanelDirectionIcon" @mousedown="handleMouseDown(6)" @mouseup="handleMouseUp()" @mouseleave="handleMouseLeave()" ondragstart="return false;" :src="'/src/assets/direction/RightTop'    + (direction === 6 ? '_c' : '') + '.png'" style="top: 15px; right: 15px;" />
                <!-- 3-右 -->
                <img class="PanTiltControlPanelDirectionIcon" @mousedown="handleMouseDown(3)" @mouseup="handleMouseUp()" @mouseleave="handleMouseLeave()" ondragstart="return false;" :src="'/src/assets/direction/Right'       + (direction === 3 ? '_c' : '') + '.png'" style="top: 95px; right: 7px;" />
                <!-- 7-右下 -->
                <img class="PanTiltControlPanelDirectionIcon" @mousedown="handleMouseDown(7)" @mouseup="handleMouseUp()" @mouseleave="handleMouseLeave()" ondragstart="return false;" :src="'/src/assets/direction/RightBottom' + (direction === 7 ? '_c' : '') + '.png'" style="bottom: 15px; right: 15px;" />
                <!-- 1-下 -->
                <img class="PanTiltControlPanelDirectionIcon" @mousedown="handleMouseDown(1)" @mouseup="handleMouseUp()" @mouseleave="handleMouseLeave()" ondragstart="return false;" :src="'/src/assets/direction/Bottom'      + (direction === 1 ? '_c' : '') + '.png'" style="bottom: 7px; left: 95px;" />
                <!-- 5-左下 -->
                <img class="PanTiltControlPanelDirectionIcon" @mousedown="handleMouseDown(5)" @mouseup="handleMouseUp()" @mouseleave="handleMouseLeave()" ondragstart="return false;" :src="'/src/assets/direction/LeftBottom'  + (direction === 5 ? '_c' : '') + '.png'" style="bottom: 15px; left: 15px;" />
                <!-- 2-左 -->
                <img class="PanTiltControlPanelDirectionIcon" @mousedown="handleMouseDown(2)" @mouseup="handleMouseUp()" @mouseleave="handleMouseLeave()" ondragstart="return false;" :src="'/src/assets/direction/Left'        + (direction === 2 ? '_c' : '') + '.png'" style="top: 95px; left: 7px;" />
                <!-- 4-左上 -->
                <img class="PanTiltControlPanelDirectionIcon" @mousedown="handleMouseDown(4)" @mouseup="handleMouseUp()" @mouseleave="handleMouseLeave()" ondragstart="return false;" :src="'/src/assets/direction/LeftTop'     + (direction === 4 ? '_c' : '') + '.png'" style="top: 15px; left: 15px;" />

                <div class="PanTiltControlPanelDirectionCenter" />
            </div>

            <div class="PanTiltControlPanelButton">
                <!-- 8-放大 -->
                <div class="PanTiltControlPanelButtonClick1" @mousedown="handleMouseDown(8)" @mouseup="handleMouseUp()" @mouseleave="handleMouseLeave()" :class="direction === 8 ? 'PanTiltControlPanelButtonClick_c' : ''">
                    放大
                </div>
                <!-- 9-缩小 -->
                <div class="PanTiltControlPanelButtonClick2" @mousedown="handleMouseDown(9)" @mouseup="handleMouseUp()" @mouseleave="handleMouseLeave()" :class="direction === 9 ? 'PanTiltControlPanelButtonClick_c' : ''">
                    缩小
                </div>
            </div>

            <div class="PanTiltControlPanelButton">
                <!-- 10-近焦距 -->
                <div class="PanTiltControlPanelButtonClick1" @mousedown="handleMouseDown(10)" @mouseup="handleMouseUp()" @mouseleave="handleMouseLeave()" :class="direction === 10 ? 'PanTiltControlPanelButtonClick_c' : ''">
                    拉近焦距
                </div>
                <!-- 11-远焦距 -->
                <div class="PanTiltControlPanelButtonClick2" @mousedown="handleMouseDown(11)" @mouseup="handleMouseUp()" @mouseleave="handleMouseLeave()" :class="direction === 11 ? 'PanTiltControlPanelButtonClick_c' : ''">
                    拉远焦距
                </div>
            </div>

            <div class="PanTiltControlPanelSpeed">
                云台速度：
                <a-radio-group v-model:value="speed" button-style="solid">
                    <a-radio-button :value="1">适中</a-radio-button>
                    <a-radio-button :value="2">快</a-radio-button>
                </a-radio-group>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'
import type { Ref } from 'vue'
import videojs from 'video.js'
import 'video.js/dist/video-js.min.css'
import { postVideoToken, getVideoList } from '/@/api/engineering'
import axios from 'axios'
import { message } from 'ant-design-vue'
import { h } from 'vue'
import { PlayCircleOutlined } from '@ant-design/icons-vue'
import { zh_cn } from '/@/ts/publicData'

videojs.addLanguage('zh-cn', zh_cn)

const options = {
    language: 'zh-cn',
}

const filterOption = (input: string, option: any) => {
    return selectVideoList.value.find(item => item.value === option.value && item.label.includes(input))
}

/**
 * 视频清晰度
 */
const quality = ref(2)
const selectVideoQuality = [
    {
        label: '流畅',
        value: 2
    },
    {
        label: '高清',
        value: 1
    },
]
/**
 * 是否按下了鼠标，只有按下鼠标才能执行 抬起和离开的事件
 */
const isDown = ref(false)
/**
 * 萤石云 操作命令
 */
const direction: Ref<number | undefined> = ref(undefined)
/**
 * 萤石云 云台速度
 */
const speed = ref(1)

/**
 * 开始云台控制
 */
const play = () => {
    axios({
        method: 'post',
        url: 'https://open.ys7.com/api/lapp/device/ptz/start',
        data: {
            accessToken,
            deviceSerial,
            channelNo,
            direction: direction.value,
            speed: speed.value
        },
        headers
    })
    .then(e => {
        // if (e.data.code === "200") {
        //     message.success('开始控制')
        // }
    })
}
/**
 * 停止云台控制
 */
const stop = (isAll?: boolean) => {
    if (isAll === true) {
        axios({
            method: 'post',
            url: 'https://open.ys7.com/api/lapp/device/ptz/stop',
            data: {
                accessToken,
                deviceSerial,
                channelNo,
            },
            headers
        })
        .then(e => {
            // if (e.data.code === "200") {
            //     message.success('停止控制')
            // }
        })
    } else {
        axios({
            method: 'post',
            url: 'https://open.ys7.com/api/lapp/device/ptz/stop',
            data: {
                accessToken,
                deviceSerial,
                channelNo,
                direction: direction.value,
            },
            headers
        })
        .then(e => {
            // if (e.data.code === "200") {
            //     message.success('停止控制')
            // }
        })
    }
}
/**
 * 鼠标按下
 */
const handleMouseDown = (num: number) => {
    isDown.value = true
    direction.value = num
    play()
}
/**
 * 鼠标抬起
 */
const handleMouseUp = () => {
    if (isDown.value === true) {
        isDown.value = false
        stop()
        direction.value = undefined
    }
}
/**
 * 鼠标离开
 */
const handleMouseLeave = () => {
    handleMouseUp()
}

const selectVideoListTime = [
    {
        label: '30秒',
        value: 30
    },
    {
        label: '5分钟',
        value: 5 * 60
    },
    {
        label: '30分钟',
        value: 30 * 60
    },
    {
        label: '1小时',
        value: 60 * 60
    },
    {
        label: '12小时',
        value: 60 * 60 * 12
    },
]
/**
 * 当前选中的时间
 */
const expireTime = ref(selectVideoListTime[1].value)
/**
 * 当前选中的摄像头
 */
const selectVideo: Ref<string> = ref()
/**
 * 下拉框数据
 */
const selectVideoList = ref([])
/**
 * 萤石云 accessToken
 */
let accessToken: string
/**
 * 请求头
 */
const headers = {
    'Content-Type': 'application/x-www-form-urlencoded',
}

const action = () => {
    if (selectVideo.value) {
        // value 的格式为 "L02937691_3" 需要按下划线分割，只取前面的字符串
        const arr = selectVideo.value.split('_')
    
        fetchVideoSrc(arr[0], Number(arr[1]))
    } else {
        message.error('请先选择摄像头！')
    }
}

onMounted(() => {
    // 获取 token
    postVideoToken()
        .then(e => {
            // console.log('返回的token：', e)

            accessToken = e
        })

    // 获取 萤石云 视频列表
    getVideoList()
    .then(e => {
        // console.log('返回的列表：', e)

        selectVideoList.value.length = 0

        e.forEach(item => {
            selectVideoList.value.push({
                value: item.key,
                label: item.name,
            })
        })
    })
})

/**
 * 设备序列号
 */
let deviceSerial: string
/**
 * 通道号
 */
let channelNo: number
/**
 * 萤石云 获取播放地址
 * @param deviceSerial 设备序列号例如427734222，均采用英文符号，限制最多50个字符
 */
const fetchVideoSrc = (serial: string, num: number) => {
    deviceSerial = serial
    channelNo = num
    axios({
        method: 'post',
        url: 'https://open.ys7.com/api/lapp/v2/live/address/get',
        data: {
            accessToken,
            protocol: 2,
            quality: quality.value,
            deviceSerial,
            channelNo
        },
        headers
    })
    .then(e => {
        if (e.data.code === '200') {
            updateVideo(e.data.data.url)
        } else {
            errVideo('https://minio.yunsxk.com:9099/xizang/video/equipmentOffline.mp4')
        }
    })
}

/**
 * 计时器
 */
let timer: NodeJS.Timeout
/**
 * 播放器实例
 */
let player

// 创建一个新的<video>元素  
const videoElement = document.createElement('video')
// 设置这个<video>元素的id和其他属性
videoElement.id = 'videoElement'
videoElement.className = 'video-js vjs-default-skin PanTiltControlVideo'
videoElement.controls = true
videoElement.autoplay = true

const src: Ref<string | undefined> = ref(undefined)

/**
 * 更新 直播视频流
 */
const updateVideo = (url: string) => {
    disposeVideo()
    src.value = url

    const cloneVideoElement = videoElement.cloneNode(true) as Element
    // 找到id为videoElementBox的元素，将新生成的video元素添加进去
    document.getElementById('videoElementBox').appendChild(cloneVideoElement)

    // 创建 新的直播流播放器
    player = videojs(cloneVideoElement, options)

    // 设置视频链接
    player.src({
        src: src.value, // 直播流地址
        type: 'application/x-mpegURL' // HLS 流的 MIME 类型
    })

    clearTimer()
    timer = setTimeout(() => {
        // 删除 原本的视频播放器和DOM
        // disposeVideo方法 返回新的 video dom元素
        disposeVideo()
        message.success('播放结束')
        direction.value = undefined
        isDown.value = false
    }, expireTime.value * 1000)

    stop(true)
}

/**
 * 报错 播放设备离线
 */
const errVideo = (url: string) => {
    disposeVideo()
    src.value = url

    const cloneVideoElement = videoElement.cloneNode(true) as Element
    // 找到id为videoElementBox的元素，将新生成的video元素添加进去
    document.getElementById('videoElementBox').appendChild(cloneVideoElement)

    // 创建 新的直播流播放器
    player = videojs(cloneVideoElement, options)

    // 设置视频链接
    player.src({
        src: src.value, // 直播流地址
        type: 'video/mp4'
    })
    
    selectVideoList.value.forEach(item => {
        if (selectVideo.value === item.value) {
            message.error(item.label + ' 设备离线！')
        }
    })

    player.play()
}

/**
 * 删除播放器，释放视频流资源，销毁video dom，添加新的video dom
 */
const disposeVideo = () => {
    // 删除 直播流播放器，释放资源。
    // 同时会删除原本的video dom元素
    // pause() 方法虽然可以暂停视频，但是无法停止持续加载的视频流
    if (player) {
        player.dispose()
        player = undefined
    }

    src.value = undefined
}

/**
 * 清除计时器
 */
const clearTimer = () => {
    if (timer) {
        window.clearInterval(timer)
    }
}

onUnmounted(() => {
    clearTimer()
    disposeVideo()
})
</script>

<style lang="less">
.PanTiltControl {
    width: 100%;
    height: 100%;
    position: relative;
    .PanTiltControlTitle {
        padding: 8px;
        background-color: white;
    }
    .PanTiltControlPanel {
        user-select: none;
        padding: 20px;
        background-color: rgba(255, 255, 255, .1669);
        box-shadow: rgba(255, 255, 255, .669) 0 30px 60px -12px inset, rgba(0, 0, 0, 0.3) 0px 18px 36px -18px inset;
        border-radius: 24px;
        position: absolute;
        top: 100px;
        right: 30px;
        width: 280px;
        opacity: .669;
        z-index: 669;
        .PanTiltControlPanelDirection {
            width: 100%;
            height: 240px;
            border-radius: 32px;
            position: relative;
            background-color: white;
            .PanTiltControlPanelDirectionIcon {
                width: 50px;
                height: 50px;
                padding: 9px;
                position: absolute;
                cursor: pointer;
            }

            .PanTiltControlPanelDirectionCenter {
                width: 112px;
                height: 112px;
                border-radius: 16px;
                box-sizing: border-box;
                border: 1px solid #F0F0F0;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background-color: #F5F5F5;
            }
        }
        .PanTiltControlPanelButton {
            margin-top: 40px;
            border-radius: 32px;
            overflow: hidden;
            .PanTiltControlPanelButtonClick1, .PanTiltControlPanelButtonClick2 {
                background-color: white;
                font-weight: 400;
                font-size: 18px;
                color: #262626;
                line-height: 64px;
                width: 120px;
                text-align: center;
                box-sizing: border-box;
                display: inline-block;
                cursor: pointer;
                transition: all .2s;
            }
            .PanTiltControlPanelButtonClick1 {
                border-right: 1px solid #F0F0F0;
            }
            .PanTiltControlPanelButtonClick2 {
                border-left: 1px solid #F0F0F0;
            }
            .PanTiltControlPanelButtonClick_c {
                color: white;
                background-color: #1890ff;
            }
        }
        .PanTiltControlPanelSpeed {
            color: white;
            margin-top: 40px;
            text-align: center;
        }
    }
    .PanTiltControlVideoBox {
        // background-color: gray;
        background-color: black;
        width: 100%;
        height: calc(100% - 48px);
        .PanTiltControlVideo {
            width: 100%;
            height: 100%;
        }
    }
}
</style>