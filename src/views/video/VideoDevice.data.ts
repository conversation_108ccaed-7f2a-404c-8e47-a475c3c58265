import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '水厂',
    align: "center",
    dataIndex: 'waterId_dictText'
  },
  {
    title: '序列号',
    align: "center",
    dataIndex: 'deviceSerial'
  },
  {
    title: '通道名称',
    align: "center",
    dataIndex: 'channelName'
  },
  {
    title: '通道号',
    align: "center",
    dataIndex: 'channelNo'
  },
  {
    title: '状态',
    align: "center",
    dataIndex: 'status_dictText'
  },
  {
    title: '是否分析',
    align: "center",
    dataIndex: 'isShared'
  },
  {
    title: '地址',
    align: "center",
    dataIndex: 'picUrl'
  },
  {
    title: '权限',
    align: "center",
    dataIndex: 'permission'
  },
  {
    title: '备注',
    align: "center",
    dataIndex: 'remarks'
  },
  {
    title: 'isAdd',
    align: "center",
    dataIndex: 'isAdd'
  },
  {
    title: '视频url',
    align: "center",
    dataIndex: 'videoUrl'
  },
  {
    title: '组态系统',
    align: "center",
    dataIndex: 'sysId_dictText'
  },
  {
    title: '监测点',
    align: "center",
    dataIndex: 'pointId_dictText'
  },
];

// 高级查询数据
export const superQuerySchema = {
  waterId: {title: '水厂',order: 0,view: 'list', type: 'string',dictTable: "water_works", dictCode: 'id', dictText: 'name',},
  deviceSerial: {title: '序列号',order: 1,view: 'text', type: 'string',},
  channelName: {title: '通道名称',order: 2,view: 'text', type: 'string',},
  channelNo: {title: '通道号',order: 3,view: 'number', type: 'number',},
  status: {title: '状态 0不在线 1在线',order: 4,view: 'number', type: 'number',dictCode: 'video_status',},
  isShared: {title: '是否分析',order: 5,view: 'text', type: 'string',},
  picUrl: {title: '地址',order: 6,view: 'text', type: 'string',},
  videoLevel: {title: '视频质量 0-流畅，1-均衡，2-高清，3-超清',order: 7,view: 'number', type: 'number',},
  isEncrypt: {title: '是否加密',order: 8,view: 'number', type: 'number',},
  permission: {title: '权限',order: 9,view: 'number', type: 'number',},
  bigFlag: {title: '大屏 0否 1是',order: 10,view: 'number', type: 'number',},
  remarks: {title: '备注',order: 11,view: 'text', type: 'string',},
  isAdd: {title: 'isAdd',order: 12,view: 'number', type: 'number',},
  videoUrl: {title: '视频url',order: 13,view: 'text', type: 'string',},
  sysId: {title: '组态系统',order: 14,view: 'list', type: 'string',dictTable: "iot_system", dictCode: 'id', dictText: 'name',},
  pointId: {title: '监测点',order: 15,view: 'list', type: 'string',dictTable: "relation_monitor", dictCode: 'id', dictText: 'name',},
};
