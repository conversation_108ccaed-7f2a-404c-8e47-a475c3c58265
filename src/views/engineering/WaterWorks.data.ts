import {BasicColumn} from '/@/components/Table';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '水厂编号',
    align: "center",
    dataIndex: 'waterWorksNum'
  },
  {
    title: '水厂名',
    align: "center",
    dataIndex: 'name'
  },
  {
    title: '水厂地址',
    align: "center",
    dataIndex: 'address'
  },
  {
    title: '经度',
    align: "center",
    dataIndex: 'longitude'
  },
  {
    title: '纬度',
    align: "center",
    dataIndex: 'latitude'
  },
  {
    title: '供水能力',
    align: "center",
    dataIndex: 'supply'
  },
  {
    title: '供水单位',
    align: "center",
    dataIndex: 'supplyUnit'
  },
  {
    title: '供水户数',
    align: "center",
    dataIndex: 'households'
  },
  {
    title: '供水人口',
    align: "center",
    dataIndex: 'populationNum'
  },
  {
    title: '服务范围',
    align: "center",
    dataIndex: 'servicesScope'
  },
  {
    title: '联系人',
    align: "center",
    dataIndex: 'contactsName'
  },
  {
    title: '联系人电话',
    align: "center",
    dataIndex: 'contactsPhone'
  },
  {
    title: '简介',
    align: "center",
    dataIndex: 'introduce'
  },
  {
    title: '水源地',
    align: "center",
    dataIndex: 'waterSource'
  },
  {
    title: '备注',
    align: "center",
    dataIndex: 'remark'
  },
  {
    title: '排序',
    align: "center",
    dataIndex: 'sort'
  },
  {
    title: '覆盖村社',
    align: "center",
    dataIndex: 'village'
  },
  {
    title: '供水面积',
    align: "center",
    dataIndex: 'supplyArea'
  },
];

// 高级查询数据
export const superQuerySchema = {
  waterWorksNum: {title: '水厂编号',order: 0,view: 'text', type: 'string',},
  name: {title: '水厂名',order: 1,view: 'text', type: 'string',},
  address: {title: '水厂地址',order: 2,view: 'text', type: 'string',},
  longitude: {title: '经度',order: 3,view: 'number', type: 'number',},
  latitude: {title: '纬度',order: 4,view: 'number', type: 'number',},
  supply: {title: '供水能力',order: 5,view: 'number', type: 'number',},
  supplyUnit: {title: '供水单位',order: 6,view: 'number', type: 'number',},
  households: {title: '供水户数',order: 8,view: 'number', type: 'number',},
  populationNum: {title: '供水人口',order: 9,view: 'number', type: 'number',},
  servicesScope: {title: '服务范围',order: 10,view: 'text', type: 'string',},
  contactsName: {title: '联系人',order: 11,view: 'text', type: 'string',},
  contactsPhone: {title: '联系人电话',order: 12,view: 'text', type: 'string',},
  introduce: {title: '简介',order: 13,view: 'text', type: 'string',},
  waterSource: {title: '水源地',order: 15,view: 'text', type: 'string',},
  remark: {title: '备注',order: 16,view: 'text', type: 'string',},
  sort: {title: '排序',order: 17,view: 'number', type: 'number',},
  village: {title: '覆盖村社',order: 18,view: 'text', type: 'string',},
  supplyArea: {title: '供水面积',order: 19,view: 'number', type: 'number',},
};
