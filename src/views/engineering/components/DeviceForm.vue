<template>
  <a-spin :spinning="confirmLoading">
    <JFormContainer :disabled="disabled">
      <template #detail>
        <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-row>
            <a-col :span="12">
              <a-form-item label="水厂" v-bind="validateInfos.waterId">
                <j-dict-select-tag v-model:value="formData.waterId" dictCode="water_works,name,id" placeholder="请选择水厂"
                  allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="设备编号" v-bind="validateInfos.code">
                <a-input v-model:value="formData.code" placeholder="请输入设备编号" allow-clear></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="设备名称" v-bind="validateInfos.name">
                <a-input v-model:value="formData.name" placeholder="请输入设备名称" allow-clear></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="规格型号" v-bind="validateInfos.model">
                <a-input v-model:value="formData.model" placeholder="请输入规格型号" allow-clear></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="设备大类" v-bind="validateInfos.type">
                <j-dict-select-tag v-model:value="formData.type" dictCode="device_type" placeholder="请选择设备大类"
                  allow-clear @change="selectChange" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="设备小类" v-bind="validateInfos.category">
                <j-dict-select-tag v-model:value="formData.category" :dictCode="smallCategory" placeholder="请选择设备小类"
                  allow-clear />
              </a-form-item>
            </a-col>
            <a-col class="PumpFormIntro" :span="24">
              <a-form-item label="设备描述" v-bind="validateInfos.content">
                <a-textarea v-model:value="formData.content" :rows="4" placeholder="请输入设备描述" />
              </a-form-item>
            </a-col>
            <a-col class="PumpFormIntro" :span="24">
              <a-form-item label="备注" v-bind="validateInfos.remark">
                <a-textarea v-model:value="formData.remark" :rows="4" placeholder="请输入备注" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </template>
    </JFormContainer>
  </a-spin>
</template>

<script lang="ts" setup>
import { ref, reactive, defineExpose, nextTick, defineProps, computed } from 'vue';
import { useMessage } from '/@/hooks/web/useMessage';
import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
import { getValueType } from '/@/utils';
import { saveOrUpdate } from '../Device.api';
import { Form } from 'ant-design-vue';
import JFormContainer from '/@/components/Form/src/container/JFormContainer.vue';
import { duplicateValidate } from '/@/utils/helper/validator'
import { getSmallCategory } from '/@/api/engineering'

const props = defineProps({
  formDisabled: { type: Boolean, default: false },
  formData: { type: Object, default: () => ({}) },
  formBpm: { type: Boolean, default: true }
});

const formRef = ref();
const useForm = Form.useForm;
const emit = defineEmits(['register', 'ok']);
const formData = reactive<Record<string, any>>({
  id: '',
  waterId: '',
  code: '',
  name: '',
  type: '',
  model: '',
  category: '',
  content: '',
  remark: '',
});
const { createMessage } = useMessage();
const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
const confirmLoading = ref<boolean>(false);
//表单验证
const validatorRules = reactive({
  waterId: [{ required: true, message: '请输入水厂!' },],
  code: [{ required: true, message: '请输入设备编号!' }, { validator: codeDuplicatevalidate }],
  name: [{ required: true, message: '请输入设备名称!' }, { validator: nameDuplicatevalidate }],
  type: [{ required: true, message: '请输入设备大类!' },],
  category: [{ required: true, message: '请输入设备小类!' },],
});
const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });

// 表单禁用
const disabled = computed(() => {
  if (props.formBpm === true) {
    if (props.formData.disabled === false) {
      return false;
    } else {
      return true;
    }
  }
  return props.formDisabled;
});


/**
 * 新增
 */
function add() {
  edit({});
}

/**
 * 编辑
 */
function edit(record) {
  nextTick(() => {
    resetFields();
    const tmpData = {};
    Object.keys(formData).forEach((key) => {
      if (record.hasOwnProperty(key)) {
        tmpData[key] = record[key]
      }
    })
    //赋值
    Object.assign(formData, tmpData);
  });
}

/**
 * 提交数据
 */
async function submitForm() {
  // 触发表单验证
  await validate();
  confirmLoading.value = true;
  const isUpdate = ref<boolean>(false);
  //时间格式化
  let model = formData;
  if (model.id) {
    isUpdate.value = true;
  }
  //循环数据
  for (let data in model) {
    //如果该数据是数组并且是字符串类型
    if (model[data] instanceof Array) {
      let valueType = getValueType(formRef.value.getProps, data);
      //如果是字符串类型的需要变成以逗号分割的字符串
      if (valueType === 'string') {
        model[data] = model[data].join(',');
      }
    }
  }
  await saveOrUpdate(model, isUpdate.value)
    .then((res) => {
      if (res.success) {
        createMessage.success(res.message);
        emit('ok');
      } else {
        createMessage.warning(res.message);
      }
    })
    .finally(() => {
      confirmLoading.value = false;
    });
}


async function codeDuplicatevalidate(_r, value) {
  return duplicateValidate('device', 'code', value, formData.id || '')
}
async function nameDuplicatevalidate(_r, value) {
  return duplicateValidate('device', 'name', value, formData.id || '')
}

const smallCategory = ref('')

const selectChange = (code: string) => {
  if (code && code !== '') {
    getSmallCategory({
      code
    })
    .then(e => {
      formData.category = ''
      smallCategory.value = e[0].dictCode
    })
  } else {
    smallCategory.value = ''
  }
}

defineExpose({
  add,
  edit,
  submitForm,
});
</script>

<style lang="less" scoped>
.antd-modal-form {
  padding: 14px;
}
</style>
