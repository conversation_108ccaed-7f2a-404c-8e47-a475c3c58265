<template>
  <a-spin :spinning="confirmLoading">
    <JFormContainer :disabled="disabled">
      <template #detail>
        <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-row>
						<a-col :span="12">
							<a-form-item label="所属水厂" v-bind="validateInfos.waterId">
								<j-dict-select-tag v-model:value="formData.waterId" dictCode="water_works,name,id" placeholder="请选择所属水厂"  allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="上级管线编号" v-bind="validateInfos.parentRelationNo">
								<a-input v-model:value="formData.parentRelationNo" placeholder="请输入上级管线编号"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="管线名称" v-bind="validateInfos.name">
								<a-input v-model:value="formData.name" placeholder="请输入管线名称"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="管线编号" v-bind="validateInfos.relationNo">
								<a-input v-model:value="formData.relationNo" placeholder="请输入管线编号"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="压力等级MPa" v-bind="validateInfos.pressureGrade">
								<a-input-number v-model:value="formData.pressureGrade" placeholder="请输入压力等级MPa" style="width: 100%" />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="铺设方式" v-bind="validateInfos.layType">
								<j-dict-select-tag v-model:value="formData.layType" dictCode="lay_type" placeholder="请选择铺设方式【1地上，2地下】"  allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="材质" v-bind="validateInfos.materialScience">
								<j-dict-select-tag v-model:value="formData.materialScience" dictCode="material_science" placeholder="请选择材质【1PE，2钢管】"  allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="管径mm" v-bind="validateInfos.diam">
								<a-input v-model:value="formData.diam" placeholder="请输入管径mm"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="地面高程m" v-bind="validateInfos.groundHeight">
								<a-input-number v-model:value="formData.groundHeight" placeholder="请输入地面高程m" style="width: 100%" />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="自由水头" v-bind="validateInfos.freeTap">
								<a-input-number v-model:value="formData.freeTap" placeholder="请输入自由水头" style="width: 100%" />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="管理单位" v-bind="validateInfos.manageUnit">
								<a-input v-model:value="formData.manageUnit" placeholder="请输入管理单位"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="建设年代" v-bind="validateInfos.buildYear">
								<a-input-number v-model:value="formData.buildYear" placeholder="请输入建设年代" style="width: 100%" />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="起始埋深" v-bind="validateInfos.beginDepth">
								<a-input-number v-model:value="formData.beginDepth" placeholder="请输入起始埋深" style="width: 100%" />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="终止埋深" v-bind="validateInfos.endDepth">
								<a-input-number v-model:value="formData.endDepth" placeholder="请输入终止埋深" style="width: 100%" />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="道路名称" v-bind="validateInfos.roadname">
								<a-input v-model:value="formData.roadname" placeholder="请输入道路名称"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="管网类别" v-bind="validateInfos.category">
								<j-dict-select-tag v-model:value="formData.category" dictCode="relation_category" placeholder="请选择管网类别【1主管，2支管】"  allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="管线段长度m" v-bind="validateInfos.relationLength">
								<a-input-number v-model:value="formData.relationLength" placeholder="请输入管线段长度m" style="width: 100%" />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="所属调节池" v-bind="validateInfos.adjustPoolId">
								<a-input-number v-model:value="formData.adjustPoolId" placeholder="请输入所属调节池" style="width: 100%" />
							</a-form-item>
						</a-col>
						<a-col class="PumpFormIntro" :span="24">
							<a-form-item label="坐标" v-bind="validateInfos.coordinate">
								<a-textarea v-model:value="formData.coordinate" :rows="4" placeholder="请输入坐标" />
							</a-form-item>
						</a-col>
						<a-col class="PumpFormIntro" :span="24">
							<a-form-item label="备注" v-bind="validateInfos.remark">
								<a-textarea v-model:value="formData.remark" :rows="4" placeholder="请输入备注" />
							</a-form-item>
						</a-col>
          </a-row>
        </a-form>
      </template>
    </JFormContainer>
  </a-spin>
</template>

<script lang="ts" setup>
  import { ref, reactive, defineExpose, nextTick, defineProps, computed, onMounted } from 'vue';
  import { defHttp } from '/@/utils/http/axios';
  import { useMessage } from '/@/hooks/web/useMessage';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import { getValueType } from '/@/utils';
  import { saveOrUpdate } from '../Relation.api';
  import { Form } from 'ant-design-vue';
  import JFormContainer from '/@/components/Form/src/container/JFormContainer.vue';
  import { duplicateValidate } from '/@/utils/helper/validator'
  
  const props = defineProps({
    formDisabled: { type: Boolean, default: false },
    formData: { type: Object, default: () => ({})},
    formBpm: { type: Boolean, default: true }
  });
  const formRef = ref();
  const useForm = Form.useForm;
  const emit = defineEmits(['register', 'ok']);
  const formData = reactive<Record<string, any>>({
    id: '',
    parentRelationNo: '',   
    name: '',   
    relationNo: '',   
    pressureGrade: undefined,
    layType: undefined,
    materialScience: undefined,
    diam: '',   
    groundHeight: undefined,
    freeTap: undefined,
    manageUnit: '',   
    buildYear: undefined,
    beginDepth: undefined,
    endDepth: undefined,
    roadname: '',   
    category: undefined,
    relationLength: undefined,
    adjustPoolId: undefined,
    waterId: undefined,
    coordinate: '',   
    deviationFlag: '',   
    remark: '',   
  });
  const { createMessage } = useMessage();
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
  const confirmLoading = ref<boolean>(false);
  //表单验证
  const validatorRules = reactive({
    name: [{ required: true, message: '请输入管线名称!'}, { validator: nameDuplicatevalidate }],
    relationNo: [{ required: true, message: '请输入管线编号!'}, { validator: relationNoDuplicatevalidate }],
    category: [{ required: true, message: '请输入管网类别【1主管，2支管】!'},],
    coordinate: [{ required: true, message: '请输入坐标!'},],
  });
  const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });

  // 表单禁用
  const disabled = computed(()=>{
    if(props.formBpm === true){
      if(props.formData.disabled === false){
        return false;
      }else{
        return true;
      }
    }
    return props.formDisabled;
  });

  
  /**
   * 新增
   */
  function add() {
    edit({});
  }

  /**
   * 编辑
   */
  function edit(record) {
    nextTick(() => {
      resetFields();
      const tmpData = {};
      Object.keys(formData).forEach((key) => {
        if(record.hasOwnProperty(key)){
          tmpData[key] = record[key]
        }
      })
      //赋值
      Object.assign(formData, tmpData);
    });
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    // 触发表单验证
    await validate();
    confirmLoading.value = true;
    const isUpdate = ref<boolean>(false);
    //时间格式化
    let model = formData;
    if (model.id) {
      isUpdate.value = true;
    }
    //循环数据
    for (let data in model) {
      //如果该数据是数组并且是字符串类型
      if (model[data] instanceof Array) {
        let valueType = getValueType(formRef.value.getProps, data);
        //如果是字符串类型的需要变成以逗号分割的字符串
        if (valueType === 'string') {
          model[data] = model[data].join(',');
        }
      }
    }
    await saveOrUpdate(model, isUpdate.value)
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
          emit('ok');
        } else {
          createMessage.warning(res.message);
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }


  async function nameDuplicatevalidate(_r, value) {
    return duplicateValidate('relation', 'name', value, formData.id || '')
  }
  async function relationNoDuplicatevalidate(_r, value) {
    return duplicateValidate('relation', 'relation_no', value, formData.id || '')
  }
  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    padding: 14px;
  }
</style>
