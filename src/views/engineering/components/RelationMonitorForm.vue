<template>
  <a-spin :spinning="confirmLoading">
    <JFormContainer :disabled="disabled">
      <template #detail>
        <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-row>
						<a-col :span="12">
							<a-form-item label="水厂" v-bind="validateInfos.waterId">
								<j-dict-select-tag v-model:value="formData.waterId" dictCode="water_works,name,id" placeholder="请选择水厂"  allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="名称" v-bind="validateInfos.name">
								<a-input v-model:value="formData.name" placeholder="请输入名称"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="编号" v-bind="validateInfos.monitorNo">
								<a-input v-model:value="formData.monitorNo" placeholder="请输入编号"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="类型" v-bind="validateInfos.type">
								<j-dict-select-tag v-model:value="formData.type" dictCode="monitor_type" placeholder="请选择类型"  allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="经度" v-bind="validateInfos.longitude">
								<a-input v-model:value="formData.longitude" placeholder="请输入经度"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="纬度" v-bind="validateInfos.latitude">
								<a-input v-model:value="formData.latitude" placeholder="请输入纬度"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="地址" v-bind="validateInfos.address">
								<a-input v-model:value="formData.address" placeholder="请输入地址"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="管径" v-bind="validateInfos.diam">
								<a-input v-model:value="formData.diam" placeholder="请输入管径"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="备注" v-bind="validateInfos.remark">
								<a-input v-model:value="formData.remark" placeholder="请输入备注"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="上级监测点" v-bind="validateInfos.parentMonitorNo">
								<a-input v-model:value="formData.parentMonitorNo" placeholder="请输入上级监测点"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
          </a-row>
        </a-form>
      </template>
    </JFormContainer>
  </a-spin>
</template>

<script lang="ts" setup>
  import { ref, reactive, defineExpose, nextTick, defineProps, computed, onMounted } from 'vue';
  import { defHttp } from '/@/utils/http/axios';
  import { useMessage } from '/@/hooks/web/useMessage';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import { getValueType } from '/@/utils';
  import { saveOrUpdate } from '../RelationMonitor.api';
  import { Form } from 'ant-design-vue';
  import JFormContainer from '/@/components/Form/src/container/JFormContainer.vue';
  import { duplicateValidate } from '/@/utils/helper/validator'
  
  const props = defineProps({
    formDisabled: { type: Boolean, default: false },
    formData: { type: Object, default: () => ({})},
    formBpm: { type: Boolean, default: true }
  });
  const formRef = ref();
  const useForm = Form.useForm;
  const emit = defineEmits(['register', 'ok']);
  const formData = reactive<Record<string, any>>({
    id: '',
    waterId: '',   
    name: '',   
    monitorNo: '',   
    type: '',   
    longitude: '',   
    latitude: '',   
    address: '',   
    diam: '',   
    remark: '',   
    parentMonitorNo: '',   
  });
  const { createMessage } = useMessage();
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
  const confirmLoading = ref<boolean>(false);
  //表单验证
  const validatorRules = reactive({
    name: [{ required: true, message: '请输入名称!'}, { validator: nameDuplicatevalidate }],
    monitorNo: [{ required: true, message: '请输入编号!'}, { validator: monitorNoDuplicatevalidate }],
    type: [{ required: true, message: '请输入类型!'},],
    longitude: [{ required: true, message: '请输入经度!'}, { pattern: /^-?\d+\.?\d*$/, message: '请输入数字!'},],
    latitude: [{ required: true, message: '请输入纬度!'}, { pattern: /^-?\d+\.?\d*$/, message: '请输入数字!'},],
  });
  const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });

  // 表单禁用
  const disabled = computed(()=>{
    if(props.formBpm === true){
      if(props.formData.disabled === false){
        return false;
      }else{
        return true;
      }
    }
    return props.formDisabled;
  });

  
  /**
   * 新增
   */
  function add() {
    edit({});
  }

  /**
   * 编辑
   */
  function edit(record) {
    nextTick(() => {
      resetFields();
      const tmpData = {};
      Object.keys(formData).forEach((key) => {
        if(record.hasOwnProperty(key)){
          tmpData[key] = record[key]
        }
      })
      //赋值
      Object.assign(formData, tmpData);
    });
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    // 触发表单验证
    await validate();
    confirmLoading.value = true;
    const isUpdate = ref<boolean>(false);
    //时间格式化
    let model = formData;
    if (model.id) {
      isUpdate.value = true;
    }
    //循环数据
    for (let data in model) {
      //如果该数据是数组并且是字符串类型
      if (model[data] instanceof Array) {
        let valueType = getValueType(formRef.value.getProps, data);
        //如果是字符串类型的需要变成以逗号分割的字符串
        if (valueType === 'string') {
          model[data] = model[data].join(',');
        }
      }
    }
    await saveOrUpdate(model, isUpdate.value)
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
          emit('ok');
        } else {
          createMessage.warning(res.message);
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }


  async function nameDuplicatevalidate(_r, value) {
    return duplicateValidate('relation_monitor', 'name', value, formData.id || '')
  }
  async function monitorNoDuplicatevalidate(_r, value) {
    return duplicateValidate('relation_monitor', 'monitor_no', value, formData.id || '')
  }
  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    padding: 14px;
  }
</style>
