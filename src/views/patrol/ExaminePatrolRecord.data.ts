import {BasicColumn} from '/@/components/Table';
import {JVxeTypes,JVxeColumn} from '/@/components/jeecg/JVxeTable/types'

//列表数据
export const columns: BasicColumn[] = [
   {
    title: '巡检名称',
    align:"center",
    dataIndex: 'name'
   },
   {
    title: '巡检人',
    align:"center",
    dataIndex: 'inspector_dictText'
   },
   {
    title: '巡检编号',
    align:"center",
    dataIndex: 'patrolCode'
   },
   {
    title: '巡检状态',
    align:"center",
    dataIndex: 'state_dictText'
   },
   {
    title: '巡检项数',
    align:"center",
    dataIndex: 'num'
   },
   {
    title: '巡检结果',
    align:"center",
    dataIndex: 'patrolEnd_dictText'
   },
   {
    title: '开始时间',
    align:"center",
    dataIndex: 'startTime'
   },
   {
    title: '结束时间',
    align:"center",
    dataIndex: 'endTime'
   },
   {
    title: '巡检类别',
    align:"center",
    dataIndex: 'taskType_dictText'
   },
   {
    title: '评估分数',
    align:"center",
    dataIndex: 'assessNum'
   },
   {
    title: '备注',
    align:"center",
    dataIndex: 'remarks'
   },
   {
    title: '水厂',
    align:"center",
    dataIndex: 'waterId_dictText'
   },
];

//子表表格配置
export const patrolRecordDetailedColumns: JVxeColumn[] = [
    {
      title: '巡检点ID',
      key: 'patrolPointId',
      type: JVxeTypes.input,
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
    {
      title: '监测点ID',
      key: 'monitorPointId',
      type: JVxeTypes.input,
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
    {
      title: '巡检点编号',
      key: 'patrolCode',
      type: JVxeTypes.input,
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
    {
      title: '巡检点名称',
      key: 'patrolPointName',
      type: JVxeTypes.input,
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
    {
      title: '序号',
      key: 'sortNo',
      type: JVxeTypes.inputNumber,
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
    {
      title: '渠道ID',
      key: 'canalId',
      type: JVxeTypes.input,
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
    {
      title: '状态 :0 1正常 2异常',
      key: 'state',
      type: JVxeTypes.input,
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
    {
      title: '问题描述',
      key: 'content',
      type: JVxeTypes.input,
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
    {
      title: '图片',
      key: 'images',
      type: JVxeTypes.input,
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
    {
      title: '严重等级',
      key: 'grade',
      type: JVxeTypes.input,
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
    {
      title: '巡检任务ID',
      key: 'patrolId',
      type: JVxeTypes.input,
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
    {
      title: '物理位置',
      key: 'address',
      type: JVxeTypes.input,
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
    {
      title: '巡检内容',
      key: 'standard',
      type: JVxeTypes.input,
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
    {
      title: '经度',
      key: 'longitude',
      type: JVxeTypes.input,
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
    {
      title: '纬度',
      key: 'latitude',
      type: JVxeTypes.input,
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
    {
      title: '备注',
      key: 'remarks',
      type: JVxeTypes.input,
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
  ]

// 高级查询数据
export const superQuerySchema = {
  name: {title: '巡检名称',order: 0,view: 'text', type: 'string',},
  inspector: {title: '巡检人',order: 1,view: 'text', type: 'string',},
  patrolCode: {title: '巡检编号',order: 2,view: 'text', type: 'string',},
  state: {title: '巡检状态',order: 3,view: 'list', type: 'string',dictCode: 'patrol_state',},
  num: {title: '巡检项数',order: 4,view: 'number', type: 'number',},
  patrolEnd: {title: '巡检结果',order: 5,view: 'text', type: 'string',},
  content: {title: '异常描述',order: 6,view: 'list', type: 'string',},
  startTime: {title: '开始时间',order: 7,view: 'datetime', type: 'string',},
  endTime: {title: '结束时间',order: 8,view: 'datetime', type: 'string',},
  taskType: {title: '巡检类别',order: 9,view: 'list', type: 'string',dictCode: 'patrol_task_type',},
  flowId: {title: '流程实例',order: 10,view: 'text', type: 'string',},
  assessNum: {title: '评估分数',order: 11,view: 'text', type: 'string',},
  remarks: {title: '备注',order: 12,view: 'text', type: 'string',},
  waterId: {title: '水厂',order: 13,view: 'text', type: 'string',},
  //子表高级查询
  patrolRecordDetailed: {
    title: '巡检明细',
    view: 'table',
    fields: {
        patrolPointId: {title: '巡检点ID',order: 0,view: 'text', type: 'string',},
        monitorPointId: {title: '监测点ID',order: 1,view: 'text', type: 'string',},
        patrolCode: {title: '巡检点编号',order: 2,view: 'text', type: 'string',},
        patrolPointName: {title: '巡检点名称',order: 3,view: 'text', type: 'string',},
        sortNo: {title: '序号',order: 4,view: 'number', type: 'number',},
        canalId: {title: '渠道ID',order: 5,view: 'text', type: 'string',},
        state: {title: '状态 :0 1正常 2异常',order: 6,view: 'text', type: 'string',},
        content: {title: '问题描述',order: 7,view: 'text', type: 'string',},
        images: {title: '图片',order: 8,view: 'text', type: 'string',},
        grade: {title: '严重等级',order: 9,view: 'text', type: 'string',},
        patrolId: {title: '巡检任务ID',order: 10,view: 'text', type: 'string',},
        address: {title: '物理位置',order: 11,view: 'text', type: 'string',},
        standard: {title: '巡检内容',order: 12,view: 'text', type: 'string',},
        longitude: {title: '经度',order: 13,view: 'text', type: 'string',},
        latitude: {title: '纬度',order: 14,view: 'text', type: 'string',},
        remarks: {title: '备注',order: 15,view: 'text', type: 'string',},
    }
  },
};
