<template>
  <div class='ly-wrap gis-map'>
    <div class="t-map map-box" id="tMap"></div>
    <div class='tree-box' v-show="showLegend">
      <div class='ly-tree-wrap'>
        <treeselect class='ly-tree-style'
                    :max-height='400'
                    :options="treeOptions"
                    :alwaysOpen='true'
                    :defaultExpandLevel='3'
                    no-options-text="暂无数据"
                    no-sub-options-text="暂无数据"
                    placeholder="请选择监测点"
                    @select="handleSelect"
        ></treeselect>
      </div>
    </div>
    <div class="locate-map" :class="{ 'active': showLegend }" @click="drawMonitor({})"></div>
    <div class="legend-map" v-show="showLegend" @click="handleShow"></div>
    <div class="legend-show" v-show="!showLegend" @click="handleShow"></div>

    <!--点位弹框-->
    <a-modal :footer="null" @ok='showInfo.show = false' v-model:open="showInfo.show"
             class="map-pop-wrap">
      <div class='map-info'>
        <div>名称：{{ showInfo.data.pointName }}</div>
        <div>编号：{{ showInfo.data.pointCode }}</div>
        <div>类型：{{ showInfo.data.typeName || '--' }}</div>
        <!--        <div>经纬度：{{ showInfo.data.lat}},{{ showInfo.data.lng}}</div>-->
        <!--        PT             压力-->
        <!--        PT_VALVE       压力&阀门-->
        <!--        PT_VALVE_FT    压力&阀门&流量-->
        <!--        PT_VALVE_LT    压力&阀门&液位-->
        <!--        WATER          水厂-->
        <!--        POOL           减压池-->

        <div v-if="showInfo.data.type==='WATER'">
          <div>当日进水量：{{
              showInfo.data.dayInFta.data || '--'
            }}{{ showInfo.data.dayInFta.unit || '--' }}
          </div>
          <div>当日出水量：{{
              showInfo.data.dayOutFta.data || '--'
            }}{{ showInfo.data.dayOutFta.unit || '--' }}
          </div>
          <div>总进水量：{{ showInfo.data.inFta.data || '--' }}{{
              showInfo.data.inFta.unit || '--'
            }}
          </div>
          <div>总出水量：{{ showInfo.data.outFta.data || '--' }}{{
              showInfo.data.outFta.unit || '--'
            }}
          </div>
        </div>

        <div v-if="showInfo.data.type==='POOL'">
          <div>液位：{{ showInfo.data.lt.data || '--' }}{{ showInfo.data.lt.unit || 'm' }}</div>
        </div>

        <div
          v-if="showInfo.data.type==='PT' || showInfo.data.type==='PT_VALVE_FT' ||showInfo.data.type==='PT_VALVE_LT' ||showInfo.data.type==='PT_VALVE'">
          压力：{{ showInfo.data.pt.data || '--' }} {{ showInfo.data.pt.unit }}
        </div>

        <div
          v-if="showInfo.data.type==='PT_VALVE_FT' ||showInfo.data.type==='PT_VALVE_LT' ||showInfo.data.type==='PT_VALVE'">
          阀门状态：
          <span v-if="showInfo.data.status.data==='1'" style="color: #1F994B">全开</span>
          <span v-if="showInfo.data.status.data==='2'" style="color: #E60E1D">全关</span>
          <span v-if="showInfo.data.status.data==='3'" style="color: #1F994B">打开中</span>
          <span v-if="showInfo.data.status.data==='4'" style="color: #E60E1D">关闭中</span>
          <span v-if="showInfo.data.status.data==='5'" style="color: #1F994B">半开</span>
          <span v-if="showInfo.data.status.data==='6'" style="color: #F3B23C">故障</span>
          <span v-if="showInfo.data.status.data==='0'" style="color: #8c8c8c">离线</span>
        </div>

        <div v-if="showInfo.data.type==='PT_VALVE_FT'">流量：{{
            showInfo.data.ft.data || '--'
          }}{{ showInfo.data.ft.unit || '--' }}
        </div>
        <div v-if="showInfo.data.type==='PT_VALVE_LT'">液位：{{
            showInfo.data.lt.data || '--'
          }}{{ showInfo.data.lt.unit || '--' }}
        </div>

        <div>最后采集时间：{{ showInfo.data.collectTime || '--' }}</div>


      </div>
      <div class='map-footer'>
        <div class='f-right' @click="handleMore(showInfo.data)">查看更多</div>
      </div>
    </a-modal>


  </div>
</template>


<script lang="ts">
import {QuestionCircleFilled} from "@ant-design/icons-vue";

var map
//这里T指向天地图对象
var T = window.T

// 页面工具包
const Util = {
  markIcon: function (data, windowInfo) {
    if (windowInfo && windowInfo.id) {
      if (data.id == windowInfo.id) {
        return {
          iconUrl: 'https://minio.yunsxk.com:9099/xizang/img/gis/' + data.type + '_3.png',
          iconSize: new T.Point(40, 70),
          iconAnchor: new T.Point(30, 60)
        }
      } else {
        return {
          iconUrl: 'https://minio.yunsxk.com:9099/xizang/img/gis/' + data.type + '_' + data.state + '.png',
          iconSize: new T.Point(32, 64),
          iconAnchor: new T.Point(25, 50)
        }
      }
    } else {
      return {
        iconUrl: 'https://minio.yunsxk.com:9099/xizang/img/gis/' + data.type + '_' + data.state + '.png',
        iconSize: new T.Point(32, 64),
        iconAnchor: new T.Point(25, 50)
      }
    }
  },
}

import {defineComponent, onActivated, onMounted, ref} from "vue";
import {getTreeList, getMonitor, getDraw, getMonitorInfo} from '/@/api/gis'
import Treeselect from "vue3-treeselect"
import 'vue3-treeselect/dist/vue3-treeselect.css'
import {useRouter} from "vue-router";

export default defineComponent({
  components: {QuestionCircleFilled, Treeselect},
  setup() {

    const router = useRouter()
    onMounted(() => {
      loadTree();
      roadMap();
    });
    // 进入页面获取种类列表
    onActivated(() => {
    })

    const handleMore = (data) => {
      if (data.type === 'WATER') {
        router.push(data.webUrl)
      } else if (data.type === 'POOL') {
        router.push(data.webUrl)
      } else {
        router.push('/inspectionCoating/monitoringPoint?id=' + showInfo.value.data.pointId)
      }
    }
    // 获取种类列表
    const treeOptions: any = ref([])
    const loadTree = async () => {
      getTreeList().then((res: any) => {
        treeOptions.value = res
      })
    }

    const drawData: any = ref([])
    const monitorData: any = ref([])

    // 初始化地图
    const roadMap = async () => {

      //初始化地图，创建一个新的地图实例
      map = new T.Map("tMap")
      map.centerAndZoom(new T.LngLat(91.99193106, 28.54083162), 12)
      map.setMapType(window.TMAP_SATELLITE_MAP) // TMAP_SATELLITE_MAP  // TMAP_HYBRID_MAP

      getDraw().then((res: any) => {
        drawData.value = res
        drawBase()
      })

      getMonitor().then((res: any) => {
        monitorData.value = res
        drawMonitor({})
      })

    }

    const showLegend = ref(<any>true);
    const handleShow = () => {
      showLegend.value = !showLegend.value
    }
    const drawBase = async () => {

      //  水渠
      drawData.value.forEach(data => {
        addLine(data, lines)
      })

    }

    const lines = ref(<any>[]);
    const label = ref(<any>{});

    const markers = ref(<any>[]);


    const addLine = async (data, arr) => {
      const tempPath = []
      data.coordinate.forEach((item) => {
        tempPath.push(new T.LngLat(item[0], item[1]))
      })
      let line = new T.Polyline(tempPath, {
        color: data.colour,
        weight: 3,
        opacity: 1,
        lineStyle: 'solid', // 实线;dashed虚线
        fillColor: 'transprent',
        fillOpacity: 0 // 透明度
      })


      line.value = line
      map.addOverLay(line)

      line.addEventListener("mouseover", function () {
        line.setWeight(6);//边界变宽
      });

      arr.push(line)
      let pathArr = []
      arr.forEach(item => {
        pathArr.push(item.getLngLats())
      })

      let expandArr = pathArr.flat().concat([]);

      line.addEventListener("mouseout", function () {
        line.setWeight(3);//边界恢复
      });

      map.setViewport(expandArr) //适应范围

    }
    const clearMap = async (type) => {

      switch (type) {
        case 'markers':
          if (markers.value) {
            markers.value.forEach((item) => {
              map.removeOverLay(item) //移除覆盖物到地图上
            })
            markers.value = null
          }
          break
        default:
          map.clearOverlays()
          break
      }

    }

    const drawMonitor = async (node) => {

      // 清除地图上的点
      if (map) {
        clearMap('markers')
      }

      let tempMarkers = []
      let tempMakerArr = []
      monitorData.value.forEach((item) => {
        let tempMarker = new T.Marker(new T.LngLat(item.longitude, item.latitude), {
          icon: new T.Icon(Util.markIcon(item, node))
        })
        map.addOverLay(tempMarker);


        markerClick(item, tempMarker);
        markerMouseover(item, tempMarker);
        markerMouseout(item, tempMarker);

        tempMarkers.push(tempMarker)
        tempMakerArr.push(new T.LngLat(item.longitude, item.latitude))
      });

      markers.value = tempMarkers


      // 如果是选点 切换中心点
      if (node && node.id) {
        map.centerAndZoom(new T.LngLat(node.longitude, node.latitude), 18);
        return
      }


      map.setViewport(tempMakerArr) //适应范围

    }

    // 获取选中的id
    const showInfo = ref(<any>{
      show: false,
      data: {}
    });

    const markerClick = async (content, marker) => {
      marker.addEventListener('click', function () {
        drawMonitor(content)
        getMonitorInfo({bizType: content.bizType, id: content.id}).then(res => {
          showInfo.value = {
            show: true,
            data: res
          }
        })
      });
    }

    const markerMouseover = async (content, marker) => {
      marker.addEventListener('mouseover', function () {
        label.value = new T.Label({
          text: `<b>${content.name}<b>`, //文本标注的内容
          position: new T.LngLat(content.longitude, content.latitude), //文本标注的地理位置
          offset: new T.Point(-40, 26) //文本标注的位置偏移值
        })

        label.value.setFontSize(10);
        label.value.setFontColor('#e1f5f8');
        label.value.setBackgroundColor('#528EC5');
        label.value.setBorderLine('none');
        label.value.setBorderColor('transparent');
        map.addOverLay(label.value);
      });
    }

    const markerMouseout = async (content, marker) => {
      marker.addEventListener('mouseout', function () {
        map.removeOverLay(label.value) //移除覆盖物到地图上
      });
    }


    // 获取选中的id
    const form = ref(<any>{
      id: null,
      name: ''
    });

    // 选择id的同时获取文本
    const handleSelect = async (node) => {
      if (node.id === '0' || node.id === '1' || node.id === '2' || node.id === '3') {
        return
      }

      monitorData.value.forEach(item => {
        if (item.id === node.id) {
          drawMonitor(item)
          return
        }
      })
    }


    /**  闸门、阀门    */


    return {
      form,
      treeOptions,
      drawData,
      monitorData,
      clearMap,
      loadTree,
      roadMap,
      drawBase,
      lines,
      label,
      markers,
      showLegend,
      handleShow,
      drawMonitor,
      markerClick,
      markerMouseover,
      markerMouseout,
      showInfo,
      handleMore,
      handleSelect
    }
  }
})
</script>


<style lang='less'>

.t-map {
  filter: url(#x-rays);
}

.t-map .tdt-control-container {
  display: none !important;
}

.t-map .tdt-label {
  border: none;
  box-shadow: none;
  background: transparent;
  padding: 0;
}

.tree-box {
  width: 240px;
  height: 480px;
  background-color: #fff;
  z-index: 9999;
  position: absolute;
  left: 20px;
  top: 20px;
}

.locate-map {
  width: 40px;
  height: 42px;
  background: url(/src/assets/images/locate.png);
  background-size: 40px;
  position: absolute;
  left: 20px;
  top: 20px;
  z-index: 9999;

  &.active {
    left: 275px;
  }
}

.ly-tree-wrap {
  width: 200px;
  height: 430px;
  position: relative;
  top: 20px;
  left: 20px;
  padding: 0;

  .ly-tree-style {

    .vue-treeselect__menu {
      position: absolute;
      top: 10px;
      height: 420px;
      border: none;
    }

    .vue-treeselect__menu::-webkit-scrollbar-track {
      -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.3);
      border-radius: 10px;
    }

    .vue-treeselect__menu::-webkit-scrollbar-thumb {
      border-radius: 10px;
      -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
    }

  }
}


.map-pop-wrap {
  width: 280px !important;

  // 标题栏
  .ant-modal-header {
    border-bottom: none;
  }


  //  底部页脚
  .ant-modal-footer {
    border-top: none;
  }

}

</style>
<style lang='less' scoped>
.gis-map {
  width: 100%;
  height: 100%;
  position: relative;

  .t-map {
    width: 100%;
    height: 100%;
    z-index: 99;
  }
}

.map-info {
  padding: 20px;
}

.map-footer {
  height: 40px;

  .f-right {
    display: inline-block;
    float: right;
    width: 140px;
    color: #1890FF;
    text-align: right;
    cursor: pointer;
    margin-right: 20px;
  }
}

.legend-map {
  width: 448px;
  height: 368px;
  z-index: 99;
  position: absolute;
  right: 20px;
  top: 20px;
  background-image: url(/src/assets/images/legend.png);
  background-size: 448px;
}

.legend-show {
  width: 40px;
  height: 42px;
  z-index: 9999;
  position: absolute;
  right: 20px;
  top: 20px;
  background-image: url(/src/assets/images/legend-show.png);
  background-size: 40px;
}

</style>
