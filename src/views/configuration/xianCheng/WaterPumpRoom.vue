<template>
    <YSXK-ScaleDiv>
        <div class="WaterPumpRoom">

            <a-button size="large" type="primary" style="position: absolute; right: 12px; top: 12px;" @click="parameterSetting.isShow = true">{{ parameterSetting.title }}</a-button>

            <YSXK-TimeUpda :position="timeUpda.position" :recentTime="timeUpda.recentTime" />

            <YSXK-DataFrame v-for="(item, i) in aData" :key="i" :position="item.position" :columnData="item.columnData">

                <div class="buttonSet" v-if="i >= 0 && i <= 3">
                    <a-popconfirm :title="'确认将 ' + item.name +' 设置为 启动？'" ok-text="是" cancel-text="否" @confirm="confirmFun('OPEN_CLOSE', item.control_0)">
                        <a-button class="greenButton" style="margin-right: 12px;">启动</a-button>
                    </a-popconfirm>
                    <a-popconfirm :title="'确认将 ' + item.name +' 设置为 停止？'" ok-text="是" cancel-text="否" @confirm="confirmFun('OPEN_CLOSE', item.control_1)">
                        <a-button class="redButton"   style="margin-right: 12px;">停止</a-button>
                    </a-popconfirm>
                    <a-popconfirm :title="'确认将 ' + item.name +' 设置为 手动？'" ok-text="是" cancel-text="否" @confirm="confirmFun('AUTO', item.control_2)">
                        <a-button class="blueButton"  style="margin-right: 12px;" :disabled="item.columnData[0].data2 === '0'">手动</a-button>
                    </a-popconfirm>
                    <a-popconfirm :title="'确认将 ' + item.name +' 设置为 自动？'" ok-text="是" cancel-text="否" @confirm="confirmFun('AUTO', item.control_3)">
                        <a-button class="blueButton"                              :disabled="item.columnData[0].data2 === '1'">自动</a-button>
                    </a-popconfirm>
                    
                    <div class="blackMask" @click="message.error('就地状态无法进行远程控制！')" v-if="item.columnData[0].data === '0'"></div>
                </div>

                <div class="buttonSet" v-else-if="i >= 4 && i <= 7">
                    <a-popconfirm :title="'确认将 ' + item.name +' 设置为 打开？'" ok-text="是" cancel-text="否" @confirm="confirmFun('OPEN_CLOSE', item.control_0)">
                        <a-button class="greenButton" style="margin-right: 12px;">打开</a-button>
                    </a-popconfirm>
                    <a-popconfirm :title="'确认将 ' + item.name +' 设置为 关闭？'" ok-text="是" cancel-text="否" @confirm="confirmFun('OPEN_CLOSE', item.control_1)">
                        <a-button class="redButton"   style="margin-right: 12px;">关闭</a-button>
                    </a-popconfirm>
                    <a-popconfirm :title="'确认将 ' + item.name +' 设置为 手动？'" ok-text="是" cancel-text="否" @confirm="confirmFun('AUTO', item.control_2)">
                        <a-button class="blueButton"  style="margin-right: 12px;" :disabled="item.columnData[0].data2 === '0'">手动</a-button>
                    </a-popconfirm>
                    <a-popconfirm :title="'确认将 ' + item.name +' 设置为 自动？'" ok-text="是" cancel-text="否" @confirm="confirmFun('AUTO', item.control_3)">
                        <a-button class="blueButton"                              :disabled="item.columnData[0].data2 === '1'">自动</a-button>
                    </a-popconfirm>
                    
                    <div class="blackMask" @click="message.error('就地状态无法进行远程控制！')" v-if="item.columnData[0].data === '0'"></div>
                </div>

                <div class="buttonSet" v-else-if="i === 10">
                    <div>
                        <a-popconfirm :title="'确认将 ' + item.name +' 设置为 打开？'" ok-text="是" cancel-text="否" @confirm="confirmFun('OPEN_CLOSE', item.control_0)">
                            <a-button class="greenButton" style="margin-right: 12px;">打开</a-button>
                        </a-popconfirm>
                        <a-popconfirm :title="'确认将 ' + item.name +' 设置为 关闭？'" ok-text="是" cancel-text="否" @confirm="confirmFun('OPEN_CLOSE', item.control_1)">
                            <a-button class="redButton">关闭</a-button>
                        </a-popconfirm>
                    </div>
                    <div style="margin-top: 12px;">
                        <a-popconfirm :title="'确认将 ' + item.name +' 设置为 手动？'" ok-text="是" cancel-text="否" @confirm="confirmFun('AUTO', item.control_2)">
                            <a-button class="blueButton"  style="margin-right: 12px;" :disabled="item.columnData[0].data2 === '0'">手动</a-button>
                        </a-popconfirm>
                        <a-popconfirm :title="'确认将 ' + item.name +' 设置为 自动？'" ok-text="是" cancel-text="否" @confirm="confirmFun('AUTO', item.control_3)">
                            <a-button class="blueButton"                              :disabled="item.columnData[0].data2 === '1'">自动</a-button>
                        </a-popconfirm>
                    </div>
                    
                    <div class="blackMask" @click="message.error('就地状态无法进行远程控制！')" v-if="item.columnData[0].data === '0'"></div>
                </div>

            </YSXK-DataFrame>

            <YSXK-ValveSwitch :modalData="parameterSetting" />

        </div>
    </YSXK-ScaleDiv>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import type { WebSocketData } from '/@/ts/publicData'
import { message } from 'ant-design-vue'
import LCG669WebSocket from '@/ts/LCG669WebSocket'

import submersibleSewagePump_green  from '/@/assets/images/configuration/xianCheng/submersibleSewagePump_green.png'
import submersibleSewagePump_orange from '/@/assets/images/configuration/xianCheng/submersibleSewagePump_orange.png'

import centrifugalPump_green  from '/@/assets/images/configuration/xianCheng/centrifugalPump_green.png'
import centrifugalPump_orange from '/@/assets/images/configuration/xianCheng/centrifugalPump_orange.png'

import electricValve_green  from '/@/assets/images/monitoringPoint/electricValve_green.png'

import YSXKValveSwitch from '/@/components/YSXK-ValveSwitch.vue'

import type { Confirm } from '/@/views/inspectionCoating/MonitoringPoint.vue'
import { postSetMonitoringPoint } from '/@/api/engineering'

/**
 * 气泡确认框 是
 */
const confirmFun = (opType: Confirm['opType'], varId: string, value?: string) => {
    postSetMonitoringPoint({
        opType,
        varId,
        value
    })
    .then(e => {
        console.log('返回信息：', e)
    })
}

const parameterSetting = reactive({
    title: '参数设置',
    isShow: false,
    arrData: [],
    inputOBJ: {
        input2: 0,
        input10: 0,
        input18: 0,
        input26: 0,
        input63: 0,
        input66: 0,
    }
})

let lcg669WebSocket: LCG669WebSocket<WebSocketData>

const assemblyData = () => {
    // console.log('lcg669WebSocket.ckData当前值：', lcg669WebSocket.ckData)
    timeUpda.recentTime = lcg669WebSocket.ckData.recentTime

    aData.value = [
        {
            // 数据框位置
            position: {
                top: '82px',
                left: '433px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '控制模式', // 标题
                    data : lcg669WebSocket.ckData.index3.data, // 值
                    data2: lcg669WebSocket.ckData.index1.data, // 值
                    type: 'AgitatorMode', // 数据类型
                },
                {
                    name: '给定频率', // 标题
                    data: lcg669WebSocket.ckData.index2.data, // 值
                    unit: lcg669WebSocket.ckData.index2.unit, // 单位
                    flag: lcg669WebSocket.ckData.index2.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
                {
                    name: '给定频率', // 标题
                    data: lcg669WebSocket.ckData.index4.data, // 值
                    unit: lcg669WebSocket.ckData.index4.unit, // 单位
                    flag: lcg669WebSocket.ckData.index4.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
            ],

            // 控制面板按钮id 启动 停止 手动 自动
            control_0: lcg669WebSocket.ckData.index5.id,
            control_1: lcg669WebSocket.ckData.index6.id,
            control_2: lcg669WebSocket.ckData.index1.id,
            control_3: lcg669WebSocket.ckData.index1.id,
            name: '1#离心泵'
        },
        {
            // 数据框位置
            position: {
                top: '259px',
                left: '433px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '控制模式', // 标题
                    data : lcg669WebSocket.ckData.index11.data, // 值
                    data2: lcg669WebSocket.ckData.index9.data, // 值
                    type: 'AgitatorMode', // 数据类型
                },
                {
                    name: '给定频率', // 标题
                    data: lcg669WebSocket.ckData.index10.data, // 值
                    unit: lcg669WebSocket.ckData.index10.unit, // 单位
                    flag: lcg669WebSocket.ckData.index10.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
                {
                    name: '给定频率', // 标题
                    data: lcg669WebSocket.ckData.index12.data, // 值
                    unit: lcg669WebSocket.ckData.index12.unit, // 单位
                    flag: lcg669WebSocket.ckData.index12.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
            ],

            // 控制面板按钮id 启动 停止 手动 自动
            control_0: lcg669WebSocket.ckData.index13.id,
            control_1: lcg669WebSocket.ckData.index14.id,
            control_2: lcg669WebSocket.ckData.index9.id,
            control_3: lcg669WebSocket.ckData.index9.id,
            name: '2#离心泵'
        },
        {
            // 数据框位置
            position: {
                top: '436px',
                left: '433px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '控制模式', // 标题
                    data : lcg669WebSocket.ckData.index19.data, // 值
                    data2: lcg669WebSocket.ckData.index17.data, // 值
                    type: 'AgitatorMode', // 数据类型
                },
                {
                    name: '给定频率', // 标题
                    data: lcg669WebSocket.ckData.index18.data, // 值
                    unit: lcg669WebSocket.ckData.index18.unit, // 单位
                    flag: lcg669WebSocket.ckData.index18.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
                {
                    name: '给定频率', // 标题
                    data: lcg669WebSocket.ckData.index20.data, // 值
                    unit: lcg669WebSocket.ckData.index20.unit, // 单位
                    flag: lcg669WebSocket.ckData.index20.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
            ],

            // 控制面板按钮id 启动 停止 手动 自动
            control_0: lcg669WebSocket.ckData.index21.id,
            control_1: lcg669WebSocket.ckData.index22.id,
            control_2: lcg669WebSocket.ckData.index17.id,
            control_3: lcg669WebSocket.ckData.index17.id,
            name: '3#离心泵'
        },
        {
            // 数据框位置
            position: {
                top: '613px',
                left: '433px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '控制模式', // 标题
                    data : lcg669WebSocket.ckData.index27.data, // 值
                    data2: lcg669WebSocket.ckData.index25.data, // 值
                    type: 'AgitatorMode', // 数据类型
                },
                {
                    name: '给定频率', // 标题
                    data: lcg669WebSocket.ckData.index26.data, // 值
                    unit: lcg669WebSocket.ckData.index26.unit, // 单位
                    flag: lcg669WebSocket.ckData.index26.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
                {
                    name: '给定频率', // 标题
                    data: lcg669WebSocket.ckData.index28.data, // 值
                    unit: lcg669WebSocket.ckData.index28.unit, // 单位
                    flag: lcg669WebSocket.ckData.index28.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
            ],

            // 控制面板按钮id 启动 停止 手动 自动
            control_0: lcg669WebSocket.ckData.index29.id,
            control_1: lcg669WebSocket.ckData.index30.id,
            control_2: lcg669WebSocket.ckData.index25.id,
            control_3: lcg669WebSocket.ckData.index25.id,
            name: '4#离心泵'
        },

        {
            // 数据框位置
            position: {
                top: '45px',
                left: '1198px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '控制模式', // 标题
                    data : lcg669WebSocket.ckData.index40.data, // 值
                    data2: lcg669WebSocket.ckData.index39.data, // 值
                    type: 'AgitatorMode', // 数据类型
                },
            ],

            // 控制面板按钮id 启动 停止 手动 自动
            control_0: lcg669WebSocket.ckData.index41.id,
            control_1: lcg669WebSocket.ckData.index42.id,
            control_2: lcg669WebSocket.ckData.index39.id,
            control_3: lcg669WebSocket.ckData.index39.id,
            name: '1#电动阀'
        },
        {
            // 数据框位置
            position: {
                top: '218px',
                left: '1198px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '控制模式', // 标题
                    data : lcg669WebSocket.ckData.index45.data, // 值
                    data2: lcg669WebSocket.ckData.index44.data, // 值
                    type: 'AgitatorMode', // 数据类型
                },
            ],

            // 控制面板按钮id 启动 停止 手动 自动
            control_0: lcg669WebSocket.ckData.index46.id,
            control_1: lcg669WebSocket.ckData.index47.id,
            control_2: lcg669WebSocket.ckData.index44.id,
            control_3: lcg669WebSocket.ckData.index44.id,
            name: '2#电动阀'
        },
        {
            // 数据框位置
            position: {
                top: '397px',
                left: '1198px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '控制模式', // 标题
                    data : lcg669WebSocket.ckData.index50.data, // 值
                    data2: lcg669WebSocket.ckData.index49.data, // 值
                    type: 'AgitatorMode', // 数据类型
                },
            ],

            // 控制面板按钮id 启动 停止 手动 自动
            control_0: lcg669WebSocket.ckData.index51.id,
            control_1: lcg669WebSocket.ckData.index52.id,
            control_2: lcg669WebSocket.ckData.index49.id,
            control_3: lcg669WebSocket.ckData.index49.id,
            name: '3#电动阀'
        },
        {
            // 数据框位置
            position: {
                top: '575px',
                left: '1198px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '控制模式', // 标题
                    data : lcg669WebSocket.ckData.index55.data, // 值
                    data2: lcg669WebSocket.ckData.index54.data, // 值
                    type: 'AgitatorMode', // 数据类型
                },
            ],

            // 控制面板按钮id 启动 停止 手动 自动
            control_0: lcg669WebSocket.ckData.index56.id,
            control_1: lcg669WebSocket.ckData.index57.id,
            control_2: lcg669WebSocket.ckData.index54.id,
            control_3: lcg669WebSocket.ckData.index54.id,
            name: '4#电动阀'
        },

        {
            // 数据框位置
            position: {
                top: '88px',
                left: '1554px'
            },
            // 每一条数据
            columnData: [
                {
                    data: lcg669WebSocket.ckData.index59.data, // 值
                    unit: lcg669WebSocket.ckData.index59.unit, // 单位
                    flag: lcg669WebSocket.ckData.index59.warningFlag, // 是否报警
                    type: 'Data', // 数据类型
                },
            ],
        },
        {
            // 数据框位置
            position: {
                top: '616px',
                left: '1554px'
            },
            // 每一条数据
            columnData: [
                {
                    data: lcg669WebSocket.ckData.index60.data, // 值
                    unit: lcg669WebSocket.ckData.index60.unit, // 单位
                    flag: lcg669WebSocket.ckData.index60.warningFlag, // 是否报警
                    type: 'Data', // 数据类型
                },
            ],
        },

        {
            // 数据框位置
            position: {
                top: '535px',
                left: '103px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '控制模式', // 标题
                    data : lcg669WebSocket.ckData.index55.data, // 值
                    data2: lcg669WebSocket.ckData.index54.data, // 值
                    type: 'AgitatorMode', // 数据类型
                },
            ],

            // 控制面板按钮id 启动 停止 手动 自动
            control_0: lcg669WebSocket.ckData.index56.id,
            control_1: lcg669WebSocket.ckData.index57.id,
            control_2: lcg669WebSocket.ckData.index54.id,
            control_3: lcg669WebSocket.ckData.index54.id,
            name: '4#电动阀'
        },

        // 所有设备状态
        {
            // 数据框位置
            position: {
                top : '-999px',
                left: '-999px'
            },
            // 每一条数据
            columnData: [
                // 潜污泵
                {
                    data : lcg669WebSocket.ckData.index37.data, // 值
                    dataX: lcg669WebSocket.ckData.index38.data, // 值
                    type: 'Img', // 数据类型
                    img : submersibleSewagePump_green,
                    imgX: submersibleSewagePump_orange,
                    // 图片位置
                    location: {
                        top: '712px',
                        left: '81px'
                    },
                    // 图片宽高
                    size: {
                        w: '87px',
                        h: '96px'
                    },
                },
                // 1#离心泵
                {
                    data : lcg669WebSocket.ckData.index7.data, // 值
                    dataX: lcg669WebSocket.ckData.index8.data, // 值
                    type: 'Img', // 数据类型
                    img : centrifugalPump_green,
                    imgX: centrifugalPump_orange,
                    // 图片位置
                    location: {
                        top: '207px',
                        left: '817px'
                    },
                    // 图片宽高
                    size: {
                        w: '240px',
                        h: '90px'
                    },
                },
                // 2#离心泵
                {
                    data : lcg669WebSocket.ckData.index15.data, // 值
                    dataX: lcg669WebSocket.ckData.index16.data, // 值
                    type: 'Img', // 数据类型
                    img : centrifugalPump_green,
                    imgX: centrifugalPump_orange,
                    // 图片位置
                    location: {
                        top: '380px',
                        left: '817px'
                    },
                    // 图片宽高
                    size: {
                        w: '240px',
                        h: '90px'
                    },
                },
                // 3#离心泵
                {
                    data : lcg669WebSocket.ckData.index23.data, // 值
                    dataX: lcg669WebSocket.ckData.index24.data, // 值
                    type: 'Img', // 数据类型
                    img : centrifugalPump_green,
                    imgX: centrifugalPump_orange,
                    // 图片位置
                    location: {
                        top: '557px',
                        left: '817px'
                    },
                    // 图片宽高
                    size: {
                        w: '240px',
                        h: '90px'
                    },
                },
                // 4#离心泵
                {
                    data : lcg669WebSocket.ckData.index31.data, // 值
                    dataX: lcg669WebSocket.ckData.index32.data, // 值
                    type: 'Img', // 数据类型
                    img : centrifugalPump_green,
                    imgX: centrifugalPump_orange,
                    // 图片位置
                    location: {
                        top: '734px',
                        left: '817px'
                    },
                    // 图片宽高
                    size: {
                        w: '240px',
                        h: '90px'
                    },
                },
                // 1#电动阀
                {
                    data : lcg669WebSocket.ckData.index43.data, // 值
                    type: 'Img', // 数据类型
                    img : electricValve_green,
                    // 图片位置
                    location: {
                        top: '107px',
                        left: '1126px'
                    },
                    // 图片宽高
                    size: {
                        w: '62px',
                        h: '80px'
                    },
                },
                // 2#电动阀
                {
                    data : lcg669WebSocket.ckData.index43.data, // 值
                    type: 'Img', // 数据类型
                    img : electricValve_green,
                    // 图片位置
                    location: {
                        top: '285px',
                        left: '1126px'
                    },
                    // 图片宽高
                    size: {
                        w: '62px',
                        h: '80px'
                    },
                },
                // 3#电动阀
                {
                    data : lcg669WebSocket.ckData.index43.data, // 值
                    type: 'Img', // 数据类型
                    img : electricValve_green,
                    // 图片位置
                    location: {
                        top: '459px',
                        left: '1126px'
                    },
                    // 图片宽高
                    size: {
                        w: '62px',
                        h: '80px'
                    },
                },
                // 4#电动阀
                {
                    data : lcg669WebSocket.ckData.index43.data, // 值
                    type: 'Img', // 数据类型
                    img : electricValve_green,
                    // 图片位置
                    location: {
                        top: '636px',
                        left: '1126px'
                    },
                    // 图片宽高
                    size: {
                        w: '62px',
                        h: '80px'
                    },
                },
            ],
        },
    ]

    console.log('打印1#电动阀数值61：', lcg669WebSocket.ckData.index61)

    parameterSetting.arrData = [
        {
            // 类型
            type  : 'frequency',
            // 名称
            name  : '1#离心泵给定频率',
            // 当前频率
            data  : lcg669WebSocket.ckData.index2.data,
            unit  : lcg669WebSocket.ckData.index2.unit,
            control_0: lcg669WebSocket.ckData.index2.id,
            inputName: 'input2'
        },
        {
            // 类型
            type  : 'frequency',
            // 名称
            name  : '2#离心泵给定频率',
            // 当前频率
            data  : lcg669WebSocket.ckData.index10.data,
            unit  : lcg669WebSocket.ckData.index10.unit,
            control_0: lcg669WebSocket.ckData.index10.id,
            inputName: 'input10'
        },
        {
            // 类型
            type  : 'frequency',
            // 名称
            name  : '3#离心泵给定频率',
            // 当前频率
            data  : lcg669WebSocket.ckData.index18.data,
            unit  : lcg669WebSocket.ckData.index18.unit,
            control_0: lcg669WebSocket.ckData.index18.id,
            inputName: 'input18'
        },
        {
            // 类型
            type  : 'frequency',
            // 名称
            name  : '4#离心泵给定频率',
            // 当前频率
            data  : lcg669WebSocket.ckData.index26.data,
            unit  : lcg669WebSocket.ckData.index26.unit,
            control_0: lcg669WebSocket.ckData.index26.id,
            inputName: 'input26'
        },
        {
            // 类型
            type: 'timeInOne',
            // 名称
            name : '1#离心泵计时',
            name2: '2#离心泵计时',
            data: lcg669WebSocket.ckData.index61.data,
            unit: lcg669WebSocket.ckData.index61.unit,
            data2: lcg669WebSocket.ckData.index62.data,
            unit2: lcg669WebSocket.ckData.index62.unit,
            control_0: lcg669WebSocket.ckData.index63.id,
            inputName: 'input63'
        },
        {
            // 类型
            type: 'timeInOne',
            // 名称
            name : '3#离心泵计时',
            name2: '4#离心泵计时',
            data: lcg669WebSocket.ckData.index64.data,
            unit: lcg669WebSocket.ckData.index64.unit,
            data2: lcg669WebSocket.ckData.index65.data,
            unit2: lcg669WebSocket.ckData.index65.unit,
            control_0: lcg669WebSocket.ckData.index66.id,
            inputName: 'input66'
        },
    ]
}

onMounted(() => {
    lcg669WebSocket = new LCG669WebSocket('ztWebsocket', 'PUMP0001' + '/web', assemblyData)
})

onUnmounted(() => {
    lcg669WebSocket.closeWebSocket()
})

const timeUpda = reactive({
    position: {
        top: '12px',
        left: '12px'
    },
    recentTime: ''
})

const aData = ref([])
</script>

<style lang="less" scoped>
.WaterPumpRoom {
    width: 100%;
    height: 100%;
    background-image: url(/src/assets/images/configuration/xiancheng/WaterPumpRoomBGC.png);
    background-size: 1688px 854px;
    background-repeat: no-repeat;
}
</style>

<style lang="less">
.WaterPumpRoom {
    .YSXK-DataFrame {
        .child {
            font-size: 16px !important;
            line-height: normal !important;
        }
    }
}
</style>