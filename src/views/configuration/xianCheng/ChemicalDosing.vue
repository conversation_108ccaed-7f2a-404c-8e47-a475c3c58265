<template>
    <YSXK-ScaleDiv>
        <div class="ChemicalDosing">

            <a-popconfirm v-if="lcg669WebSocket" :title="'确认进行 PAC一键配药 ？'" ok-text="是" cancel-text="否" @confirm="confirmFun('OPEN_CLOSE', lcg669WebSocket.ckData.index37.id)">
                <a-button class="greenButton" style="position: absolute; left: 313px; top: 372px;">PAC一键配药</a-button>
            </a-popconfirm>
            <a-popconfirm v-if="lcg669WebSocket" :title="'确认进行 PAM一键配药 ？'" ok-text="是" cancel-text="否" @confirm="confirmFun('OPEN_CLOSE', lcg669WebSocket.ckData.index38.id)">
                <a-button class="greenButton" style="position: absolute; left: 1286px; top: 110px;">PAM一键配药</a-button>
            </a-popconfirm>

            <YSXK-TimeUpda :position="timeUpda.position" :recentTime="timeUpda.recentTime" />

            <YSXK-DataFrame v-for="(item, i) in aData" :key="i" :position="item.position" :columnData="item.columnData">

                <div class="buttonSet">
                    <div>
                        <a-popconfirm :title="'确认将 ' + item.name +' 设置为 打开？'" ok-text="是" cancel-text="否" @confirm="confirmFun('OPEN_CLOSE', item.control_0)">
                            <a-button class="greenButton" style="margin-right: 12px;">打开</a-button>
                        </a-popconfirm>
                        <a-popconfirm :title="'确认将 ' + item.name +' 设置为 关闭？'" ok-text="是" cancel-text="否" @confirm="confirmFun('OPEN_CLOSE', item.control_1)">
                            <a-button class="redButton">关闭</a-button>
                        </a-popconfirm>
                    </div>
                    <div style="margin-top: 12px;">
                        <a-popconfirm :title="'确认将 ' + item.name +' 设置为 手动？'" ok-text="是" cancel-text="否" @confirm="confirmFun('AUTO', item.control_2)">
                            <a-button class="blueButton"  style="margin-right: 12px;" :disabled="item.columnData[0].data2 === '0'">手动</a-button>
                        </a-popconfirm>
                        <a-popconfirm :title="'确认将 ' + item.name +' 设置为 自动？'" ok-text="是" cancel-text="否" @confirm="confirmFun('AUTO', item.control_3)">
                            <a-button class="blueButton"                              :disabled="item.columnData[0].data2 === '1'">自动</a-button>
                        </a-popconfirm>
                    </div>
                    
                    <div class="blackMask" @click="message.error('就地状态无法进行远程控制！')" v-if="item.columnData[0].data === '0'"></div>
                </div>

            </YSXK-DataFrame>

        </div>
    </YSXK-ScaleDiv>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import type { WebSocketData } from '/@/ts/publicData'
import { message } from 'ant-design-vue'
import LCG669WebSocket from '@/ts/LCG669WebSocket'

import PAC1_green  from '/@/assets/images/configuration/xianCheng/PAC1_green.png'
import PAC1_orange from '/@/assets/images/configuration/xianCheng/PAC1_orange.png'
import PAC2_green  from '/@/assets/images/configuration/xianCheng/PAC2_green.png'
import PAC2_orange from '/@/assets/images/configuration/xianCheng/PAC2_orange.png'
import PAC3_green  from '/@/assets/images/configuration/xianCheng/PAC3_green.png'
import PAC3_orange from '/@/assets/images/configuration/xianCheng/PAC3_orange.png'
import PAM1_green  from '/@/assets/images/configuration/xianCheng/PAM1_green.png'
import PAM1_orange from '/@/assets/images/configuration/xianCheng/PAM1_orange.png'
import PAM2_green  from '/@/assets/images/configuration/xianCheng/PAM2_green.png'
import PAM2_orange from '/@/assets/images/configuration/xianCheng/PAM2_orange.png'
import PAM3_green  from '/@/assets/images/configuration/xianCheng/PAM3_green.png'
import PAM3_orange from '/@/assets/images/configuration/xianCheng/PAM3_orange.png'

import type { Confirm } from '/@/views/inspectionCoating/MonitoringPoint.vue'
import { postSetMonitoringPoint } from '/@/api/engineering'

/**
 * 气泡确认框 是
 */
const confirmFun = (opType: Confirm['opType'], varId: string, value?: string) => {
    postSetMonitoringPoint({
        opType,
        varId,
        value
    })
    .then(e => {
        console.log('返回信息：', e)
    })
}

let lcg669WebSocket: LCG669WebSocket<WebSocketData>

const assemblyData = () => {
    // console.log('lcg669WebSocket.ckData当前值：', lcg669WebSocket.ckData)
    timeUpda.recentTime = lcg669WebSocket.ckData.recentTime

    aData.value = [
        {
            // 数据框位置
            position: {
                top: '150px',
                left: '596px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '状态', // 标题
                    data  : lcg669WebSocket.ckData.index2.data, // 值
                    data2 : lcg669WebSocket.ckData.index1.data, // 值
                    data3 : lcg669WebSocket.ckData.index3.data, // 值
                    dataX3: lcg669WebSocket.ckData.index4.data, // 值
                    type: 'DosingPump', // 数据类型
                    img : PAC1_green,
                    imgX: PAC1_orange,
                    // 图片位置
                    location: {
                        top: '324px',
                        left: '662px'
                    },
                    // 图片宽高
                    size: {
                        w: '66px',
                        h: '87px'
                    },
                },
            ],

            // 控制面板按钮id 启动 停止 手动 自动
            control_0: lcg669WebSocket.ckData.index5.id,
            control_1: lcg669WebSocket.ckData.index6.id,
            control_2: lcg669WebSocket.ckData.index1.id,
            control_3: lcg669WebSocket.ckData.index1.id,
            name: 'PAC 1#加药泵'
        },
        {
            // 数据框位置
            position: {
                top: '470px',
                left: '486px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '状态', // 标题
                    data  : lcg669WebSocket.ckData.index8.data, // 值
                    data2 : lcg669WebSocket.ckData.index7.data, // 值
                    data3 : lcg669WebSocket.ckData.index9.data, // 值
                    dataX3: lcg669WebSocket.ckData.index10.data, // 值
                    type: 'DosingPump', // 数据类型
                    img : PAC2_green,
                    imgX: PAC2_orange,
                    // 图片位置
                    location: {
                        top: '647px',
                        left: '531px'
                    },
                    // 图片宽高
                    size: {
                        w: '81px',
                        h: '95px'
                    },
                },
            ],

            // 控制面板按钮id 启动 停止 手动 自动
            control_0: lcg669WebSocket.ckData.index11.id,
            control_1: lcg669WebSocket.ckData.index12.id,
            control_2: lcg669WebSocket.ckData.index7.id,
            control_3: lcg669WebSocket.ckData.index7.id,
            name: 'PAC 2#加药泵'
        },
        {
            // 数据框位置
            position: {
                top: '470px',
                left: '692px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '状态', // 标题
                    data  : lcg669WebSocket.ckData.index14.data, // 值
                    data2 : lcg669WebSocket.ckData.index13.data, // 值
                    data3 : lcg669WebSocket.ckData.index15.data, // 值
                    dataX3: lcg669WebSocket.ckData.index16.data, // 值
                    type: 'DosingPump', // 数据类型
                    img : PAC3_green,
                    imgX: PAC3_orange,
                    // 图片位置
                    location: {
                        top: '647px',
                        left: '732px'
                    },
                    // 图片宽高
                    size: {
                        w: '78px',
                        h: '95px'
                    },
                },
            ],

            // 控制面板按钮id 启动 停止 手动 自动
            control_0: lcg669WebSocket.ckData.index17.id,
            control_1: lcg669WebSocket.ckData.index18.id,
            control_2: lcg669WebSocket.ckData.index13.id,
            control_3: lcg669WebSocket.ckData.index13.id,
            name: 'PAC 3#加药泵'
        },

        {
            // 数据框位置
            position: {
                top: '251px',
                left: '1209px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '状态', // 标题
                    data  : lcg669WebSocket.ckData.index20.data, // 值
                    data2 : lcg669WebSocket.ckData.index19.data, // 值
                    data3 : lcg669WebSocket.ckData.index21.data, // 值
                    dataX3: lcg669WebSocket.ckData.index22.data, // 值
                    type: 'DosingPump', // 数据类型
                    img : PAM1_green,
                    imgX: PAM1_orange,
                    // 图片位置
                    location: {
                        top: '256px',
                        left: '1124px'
                    },
                    // 图片宽高
                    size: {
                        w: '72px',
                        h: '165px'
                    },
                },
            ],

            // 控制面板按钮id 启动 停止 手动 自动
            control_0: lcg669WebSocket.ckData.index23.id,
            control_1: lcg669WebSocket.ckData.index24.id,
            control_2: lcg669WebSocket.ckData.index19.id,
            control_3: lcg669WebSocket.ckData.index19.id,
            name: 'PAM 1#加药泵'
        },
        {
            // 数据框位置
            position: {
                top: '432px',
                left: '1251px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '状态', // 标题
                    data  : lcg669WebSocket.ckData.index26.data, // 值
                    data2 : lcg669WebSocket.ckData.index25.data, // 值
                    data3 : lcg669WebSocket.ckData.index27.data, // 值
                    dataX3: lcg669WebSocket.ckData.index28.data, // 值
                    type: 'DosingPump', // 数据类型
                    img : PAM2_green,
                    imgX: PAM2_orange,
                    // 图片位置
                    location: {
                        top: '488px',
                        left: '1178px'
                    },
                    // 图片宽高
                    size: {
                        w: '57px',
                        h: '101px'
                    },
                },
            ],

            // 控制面板按钮id 启动 停止 手动 自动
            control_0: lcg669WebSocket.ckData.index29.id,
            control_1: lcg669WebSocket.ckData.index30.id,
            control_2: lcg669WebSocket.ckData.index25.id,
            control_3: lcg669WebSocket.ckData.index25.id,
            name: 'PAM 2#加药泵'
        },
        {
            // 数据框位置
            position: {
                top: '622px',
                left: '1288px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '状态', // 标题
                    data  : lcg669WebSocket.ckData.index32.data, // 值
                    data2 : lcg669WebSocket.ckData.index31.data, // 值
                    data3 : lcg669WebSocket.ckData.index33.data, // 值
                    dataX3: lcg669WebSocket.ckData.index34.data, // 值
                    type: 'DosingPump', // 数据类型
                    img : PAM3_green,
                    imgX: PAM3_orange,
                    // 图片位置
                    location: {
                        top: '668px',
                        left: '1209px'
                    },
                    // 图片宽高
                    size: {
                        w: '68px',
                        h: '110px'
                    },
                },
            ],

            // 控制面板按钮id 启动 停止 手动 自动
            control_0: lcg669WebSocket.ckData.index35.id,
            control_1: lcg669WebSocket.ckData.index36.id,
            control_2: lcg669WebSocket.ckData.index31.id,
            control_3: lcg669WebSocket.ckData.index31.id,
            name: 'PAM 3#加药泵'
        },
    ]
}

onMounted(() => {
    lcg669WebSocket = new LCG669WebSocket('ztWebsocket', 'PAC002' + '/web', assemblyData)
})

onUnmounted(() => {
    lcg669WebSocket.closeWebSocket()
})

const timeUpda = reactive({
    position: {
        top: '12px',
        left: '12px'
    },
    recentTime: ''
})

const aData = ref([])
</script>

<style lang="less" scoped>
.ChemicalDosing {
    width: 100%;
    height: 100%;
    background-image: url(/src/assets/images/configuration/xiancheng/ChemicalDosingBGC.png);
    background-size: 1688px 854px;
    background-repeat: no-repeat;
}
</style>

<style lang="less">
.ChemicalDosing {
    .YSXK-DataFrame {
        .child {
            font-size: 16px !important;
            line-height: normal !important;
        }
    }
}
</style>