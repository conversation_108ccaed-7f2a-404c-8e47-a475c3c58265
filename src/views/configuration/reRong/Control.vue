<template>
    <div class="Control">
        <YSXK-TimeUpda
            :position  ="timeUpda.position"
            :recentTime="timeUpda.recentTime"
        />

        <div class="title">热荣水厂排泥控制</div>
        
        <template v-if="isShow">
            <div class="panelBox">
                <div class="panel buttonSet">
                    <div class="panelTop">
                        <div class="name">1#沉淀池</div>
                        <div class="data" v-if     ="sedimentationBasin1.panelTop.data  === '1'"                        >远程</div>
                        <div class="data" v-else-if="sedimentationBasin1.panelTop.data  === '0'" style="color: #FDAC36;">就地</div>
                        <div class="data" v-if     ="sedimentationBasin1.panelTop.data2 === '1'" style="color: #FDAC36;">自动</div>
                        <div class="data" v-else-if="sedimentationBasin1.panelTop.data2 === '0'"                        >手动</div>
                        <div class="data" v-if     ="sedimentationBasin1.panelTop.data3 === '1'"                        >排泥中</div>
                        <div class="data" v-else-if="sedimentationBasin1.panelTop.data3 === '0'" style="color: #FDAC36;">未排泥</div>
                    </div>
    
                    <div class="panelButton">
                        <a-popconfirm title="确认将 1#沉淀池 设置为 手动？" ok-text="是" cancel-text="否" @confirm="confirmFun('AUTO', sedimentationBasin1.panelTop.control_0)">
                            <a-button type="primary" class="button" :disabled="sedimentationBasin1.panelTop.data2 === '0'"                           >手动</a-button>
                        </a-popconfirm>
                        <a-popconfirm title="确认将 1#沉淀池 设置为 自动？" ok-text="是" cancel-text="否" @confirm="confirmFun('AUTO', sedimentationBasin1.panelTop.control_1)">
                            <a-button type="primary" class="button" :disabled="sedimentationBasin1.panelTop.data2 === '1'" style="margin-left: 12px;">自动</a-button>
                        </a-popconfirm>
                    </div>
    
                    <div class="panelSet">
                        <div class="panelSetItem">
                            <div class="panelSetName">
                                {{ sedimentationBasin1.set[0].name + '：' }}
                            </div>
                            <div class="panelSetData">
                                {{ sedimentationBasin1.set[0].data }}
                            </div>
                            <div class="panelSetUnit">
                                {{ sedimentationBasin1.set[0].unit }}
                            </div>
                        </div>
                        <div class="panelSetItem">
                            <div class="panelSetName">
                                {{ sedimentationBasin1.set[1].name + '：' }}
                            </div>
                            <div class="panelSetData">
                                {{ sedimentationBasin1.set[1].data }}
                            </div>
                            <div class="panelSetUnit">
                                {{ sedimentationBasin1.set[1].unit }}
                            </div>
                        </div>
                        <div class="panelSetItem">
                            <div class="panelSetName">
                                {{ sedimentationBasin1.set[2].name + '：' }}
                            </div>
                            <div class="panelSetData">
                                {{ sedimentationBasin1.set[2].data }}
                            </div>
                            <div class="panelSetUnit">
                                {{ sedimentationBasin1.set[2].unit }}
                            </div>
                        </div>
                        <div class="panelSetItem">
                            <div class="panelSetName">
                                {{ sedimentationBasin1.set[3].name + '：' }}
                            </div>
                            <div class="panelSetData">
                                {{ sedimentationBasin1.set[3].data }}
                            </div>
                            <div class="panelSetUnit">
                                {{ sedimentationBasin1.set[3].unit }}
                            </div>
                        </div>
                    </div>
    
                    <div class="panelOneClick">
                        <a-popconfirm title="启动所有电磁阀？" ok-text="是" cancel-text="否" @confirm="confirmFun('AUTO', sedimentationBasin1.panelTop.control_2)">
                            <a-button class="greenButton" style="margin-right: 12px;">一键启动</a-button>
                        </a-popconfirm>
                        <a-popconfirm title="停止所有电磁阀？" ok-text="是" cancel-text="否" @confirm="confirmFun('AUTO', sedimentationBasin1.panelTop.control_3)">
                            <a-button class="redButton" style="margin-right: 12px;">一键停止</a-button>
                        </a-popconfirm>
                        <a-popconfirm title="复位所有电磁阀？" ok-text="是" cancel-text="否" @confirm="confirmFun('RESET_FAULT', sedimentationBasin1.panelTop.control_4)">
                            <a-button class="blueButton">复位</a-button>
                        </a-popconfirm>
                    </div>
    
                    <div class="blackMask" style="border-radius: 12px;" @click="errM" v-if="sedimentationBasin1.panelTop.data === '0'"></div>
                </div>
                <div class="solenoidValveItem" :class="item.state === '1' ? 'solenoidValveItem_c' : ''" v-for="(item, i) in sedimentationBasin1.valve" :key="i">
                    <div class="solenoidValveTitle">{{ item.title }}</div>
    
                    <img class="solenoidValveBGC" src="/src/assets/images/configuration/reRong/handleBGC.png" />
    
                    <div class="solenoidValveRound" :class="item.state === '1' ? '' : 'solenoidValveRound_c'"></div>
    
                    <template v-if="item.state === '1'">
                        <a-popconfirm :title="'确认' + (item.onOff === '1' ? '关闭' : '打开') + ' ' + (i + 1) + '号电磁阀？'" ok-text="是" cancel-text="否" @confirm="confirmFun('AUTO', item.controlId)">
                            <img class="solenoidValveOnOff" :class="item.onOff === '1' ? 'solenoidValveOnOff_c' : ''" src="/src/assets/images/configuration/reRong/handle_c.png" />
                        </a-popconfirm>
                    </template>
                    <template v-else>
                        <img class="solenoidValveOnOff" :class="item.onOff === '1' ? 'solenoidValveOnOff_c' : ''" src="/src/assets/images/configuration/reRong/handle.png" style="cursor: not-allowed;" @click="errM" />
                    </template>
                </div>
            </div>
            
            <div class="panelBox">
                <div class="panel buttonSet">
                    <div class="panelTop">
                        <div class="name">2#沉淀池</div>
                        <div class="data" v-if     ="sedimentationBasin2.panelTop.data  === '1'"                        >远程</div>
                        <div class="data" v-else-if="sedimentationBasin2.panelTop.data  === '0'" style="color: #FDAC36;">就地</div>
                        <div class="data" v-if     ="sedimentationBasin2.panelTop.data2 === '1'" style="color: #FDAC36;">自动</div>
                        <div class="data" v-else-if="sedimentationBasin2.panelTop.data2 === '0'"                        >手动</div>
                        <div class="data" v-if     ="sedimentationBasin2.panelTop.data3 === '1'"                        >排泥中</div>
                        <div class="data" v-else-if="sedimentationBasin2.panelTop.data3 === '0'" style="color: #FDAC36;">未排泥</div>
                    </div>
    
                    <div class="panelButton">
                        <a-popconfirm title="确认将 2#沉淀池 设置为 手动？" ok-text="是" cancel-text="否" @confirm="confirmFun('AUTO', sedimentationBasin2.panelTop.control_2)">
                            <a-button type="primary" class="button" :disabled="sedimentationBasin2.panelTop.data2 === '0'"                           >手动</a-button>
                        </a-popconfirm>
                        <a-popconfirm title="确认将 2#沉淀池 设置为 自动？" ok-text="是" cancel-text="否" @confirm="confirmFun('AUTO', sedimentationBasin2.panelTop.control_2)">
                            <a-button type="primary" class="button" :disabled="sedimentationBasin2.panelTop.data2 === '1'" style="margin-left: 12px;">自动</a-button>
                        </a-popconfirm>
                    </div>
    
                    <div class="panelSet">
                        <div class="panelSetItem">
                            <div class="panelSetName">
                                {{ sedimentationBasin2.set[0].name + '：' }}
                            </div>
                            <div class="panelSetData">
                                {{ sedimentationBasin2.set[0].data }}
                            </div>
                            <div class="panelSetUnit">
                                {{ sedimentationBasin2.set[0].unit }}
                            </div>
                        </div>
                        <div class="panelSetItem">
                            <div class="panelSetName">
                                {{ sedimentationBasin2.set[1].name + '：' }}
                            </div>
                            <div class="panelSetData">
                                {{ sedimentationBasin2.set[1].data }}
                            </div>
                            <div class="panelSetUnit">
                                {{ sedimentationBasin2.set[1].unit }}
                            </div>
                        </div>
                        <div class="panelSetItem">
                            <div class="panelSetName">
                                {{ sedimentationBasin2.set[2].name + '：' }}
                            </div>
                            <div class="panelSetData">
                                {{ sedimentationBasin2.set[2].data }}
                            </div>
                            <div class="panelSetUnit">
                                {{ sedimentationBasin2.set[2].unit }}
                            </div>
                        </div>
                        <div class="panelSetItem">
                            <div class="panelSetName">
                                {{ sedimentationBasin2.set[3].name + '：' }}
                            </div>
                            <div class="panelSetData">
                                {{ sedimentationBasin2.set[3].data }}
                            </div>
                            <div class="panelSetUnit">
                                {{ sedimentationBasin2.set[3].unit }}
                            </div>
                        </div>
                    </div>
    
                    <div class="panelOneClick">
                        <a-popconfirm title="启动所有电磁阀？" ok-text="是" cancel-text="否" @confirm="confirmFun('AUTO', sedimentationBasin2.panelTop.control_2)">
                            <a-button class="greenButton" style="margin-right: 12px;">一键启动</a-button>
                        </a-popconfirm>
                        <a-popconfirm title="停止所有电磁阀？" ok-text="是" cancel-text="否" @confirm="confirmFun('AUTO', sedimentationBasin2.panelTop.control_3)">
                            <a-button class="redButton" style="margin-right: 12px;">一键停止</a-button>
                        </a-popconfirm>
                        <a-popconfirm title="复位所有电磁阀？" ok-text="是" cancel-text="否" @confirm="confirmFun('RESET_FAULT', sedimentationBasin2.panelTop.control_4)">
                            <a-button class="blueButton">复位</a-button>
                        </a-popconfirm>
                    </div>
    
                    <div class="blackMask" style="border-radius: 12px;" @click="errM" v-if="sedimentationBasin2.panelTop.data === '0'"></div>
                </div>
                <div class="solenoidValveItem" :class="item.state === '1' ? 'solenoidValveItem_c' : ''" v-for="(item, i) in sedimentationBasin2.valve" :key="i">
                    <div class="solenoidValveTitle">{{ item.title }}</div>
    
                    <img class="solenoidValveBGC" src="/src/assets/images/configuration/reRong/handleBGC.png" />
    
                    <div class="solenoidValveRound" :class="item.state === '1' ? '' : 'solenoidValveRound_c'"></div>
    
                    <template v-if="item.state === '1'">
                        <a-popconfirm :title="'确认' + (item.onOff === '1' ? '关闭' : '打开') + ' ' + (i + 1 + sedimentationBasin2.valve.length) + '号电磁阀？'" ok-text="是" cancel-text="否" @confirm="confirmFun('AUTO', item.controlId)">
                            <img class="solenoidValveOnOff" :class="item.onOff === '1' ? 'solenoidValveOnOff_c' : ''" src="/src/assets/images/configuration/reRong/handle_c.png" />
                        </a-popconfirm>
                    </template>
                    <template v-else>
                        <img class="solenoidValveOnOff" :class="item.onOff === '1' ? 'solenoidValveOnOff_c' : ''" src="/src/assets/images/configuration/reRong/handle.png" style="cursor: not-allowed;" @click="errM" />
                    </template>
                </div>
            </div>
        </template>

    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { postSetMonitoringPoint } from '/@/api/engineering'
import type { Confirm } from '../../inspectionCoating/MonitoringPoint.vue'
import { message } from 'ant-design-vue'
import LCG669WebSocket from '@/ts/LCG669WebSocket'
import type { WebSocketData } from '/@/ts/publicData'

const isShow = ref(false)

let lcg669WebSocket: LCG669WebSocket<WebSocketData>

const errM = () => {
    message.error('就地状态无法进行远程控制！')
}

const sedimentationBasin1 = ref({
    panelTop: {
        // 就地
        data: '',
        // 自动
        data2: '',
        // 排泥中
        data3: '',
        control_0: '',
        control_1: '',
        control_2: '',
        control_3: '',
        control_4: '',
    },
    set: [
        {
            name: '排泥周期',
            data: '',
            unit: ''
        },
        {
            name: '单阀时长',
            data: '',
            unit: ''
        },
        {
            name: '间隔时长',
            data: '',
            unit: ''
        },
        {
            name: '倒计时',
            data: '',
            unit: ''
        },
    ],
    valve: [
        {
            title: '1号电磁阀',
            onOff: '',
            state: '',
            controlId: ''
        },
        {
            title: '2号电磁阀',
            onOff: '',
            state: '',
            controlId: ''
        },
        {
            title: '3号电磁阀',
            onOff: '',
            state: '',
            controlId: ''
        },
        {
            title: '4号电磁阀',
            onOff: '',
            state: '',
            controlId: ''
        },
        {
            title: '5号电磁阀',
            onOff: '',
            state: '',
            controlId: ''
        },
    ]
})

const sedimentationBasin2 = ref({
    panelTop: {
        // 就地
        data: '',
        // 自动
        data2: '',
        // 排泥中
        data3: '',
        control_0: '',
        control_1: '',
        control_2: '',
        control_3: '',
        control_4: '',
    },
    set: [
        {
            name: '排泥周期',
            data: '',
            unit: ''
        },
        {
            name: '单阀时长',
            data: '',
            unit: ''
        },
        {
            name: '间隔时长',
            data: '',
            unit: ''
        },
        {
            name: '倒计时',
            data: '',
            unit: ''
        },
    ],
    valve: [
        {
            title: '6号电磁阀',
            onOff: '',
            state: '',
            controlId: ''
        },
        {
            title: '7号电磁阀',
            onOff: '',
            state: '',
            controlId: ''
        },
        {
            title: '8号电磁阀',
            onOff: '',
            state: '',
            controlId: ''
        },
        {
            title: '9号电磁阀',
            onOff: '',
            state: '',
            controlId: ''
        },
        {
            title: '10号电磁阀',
            onOff: '',
            state: '',
            controlId: ''
        },
    ]
})

/**
 * 气泡确认框 是
 */
const confirmFun = (opType: Confirm['opType'], varId: string, value?: string) => {
    postSetMonitoringPoint({
        opType,
        varId,
        value
    })
    .then(e => {
        
    })
}

const timeUpda = reactive({
    position: {
        top: '12px',
        left: '12px'
    },
    recentTime: ''
})

const assemblyData = () => {
    // console.log('lcg669WebSocket.ckData.index1.data当前值：', lcg669WebSocket.ckData.index1.data)

    isShow.value = true

    timeUpda.recentTime = lcg669WebSocket.ckData.recentTime

    sedimentationBasin1.value = {
        panelTop: {
            // 就地
            data: lcg669WebSocket.ckData.index8.data,
            // 自动
            data2: lcg669WebSocket.ckData.index13.data,
            // 排泥中
            data3: lcg669WebSocket.ckData.index9.data,
            control_0: lcg669WebSocket.ckData.index14.id,
            control_1: lcg669WebSocket.ckData.index14.id,
            control_2: lcg669WebSocket.ckData.index10.id,
            control_3: lcg669WebSocket.ckData.index11.id,
            control_4: lcg669WebSocket.ckData.index51.id
        },
        set: [
            {
                name: '排泥周期',
                data: lcg669WebSocket.ckData.index1.data,
                unit: lcg669WebSocket.ckData.index1.unit
            },
            {
                name: '单阀时长',
                data: lcg669WebSocket.ckData.index3.data,
                unit: lcg669WebSocket.ckData.index3.unit
            },
            {
                name: '间隔时长',
                data: lcg669WebSocket.ckData.index5.data,
                unit: lcg669WebSocket.ckData.index5.unit
            },
            {
                name: '倒计时',
                data: '-',
                unit: '-'
                // data: lcg669WebSocket.ckData.index7.data,
                // unit: lcg669WebSocket.ckData.index7.unit
            },
        ],
        valve: [
            {
                title: '1号电磁阀',
                onOff: lcg669WebSocket.ckData.index41.data,
                state: lcg669WebSocket.ckData.index33.data,
                controlId: lcg669WebSocket.ckData.index42.id
            },
            {
                title: '2号电磁阀',
                onOff: lcg669WebSocket.ckData.index43.data,
                state: lcg669WebSocket.ckData.index33.data,
                controlId: lcg669WebSocket.ckData.index44.id
            },
            {
                title: '3号电磁阀',
                onOff: lcg669WebSocket.ckData.index45.data,
                state: lcg669WebSocket.ckData.index33.data,
                controlId: lcg669WebSocket.ckData.index46.id
            },
            {
                title: '4号电磁阀',
                onOff: lcg669WebSocket.ckData.index47.data,
                state: lcg669WebSocket.ckData.index33.data,
                controlId: lcg669WebSocket.ckData.index48.id
            },
            {
                title: '5号电磁阀',
                onOff: lcg669WebSocket.ckData.index49.data,
                state: lcg669WebSocket.ckData.index33.data,
                controlId: lcg669WebSocket.ckData.index50.id
            },
        ]
    }

    sedimentationBasin2.value = {
        panelTop: {
            // 就地
            data: lcg669WebSocket.ckData.index33.data,
            // 自动
            data2: lcg669WebSocket.ckData.index38.data,
            // 排泥中
            data3: lcg669WebSocket.ckData.index34.data,
            control_0: lcg669WebSocket.ckData.index39.id,
            control_1: lcg669WebSocket.ckData.index39.id,
            control_2: lcg669WebSocket.ckData.index35.id,
            control_3: lcg669WebSocket.ckData.index36.id,
            control_4: lcg669WebSocket.ckData.index52.id
        },
        set: [
            {
                name: '排泥周期',
                data: lcg669WebSocket.ckData.index26.data,
                unit: lcg669WebSocket.ckData.index26.unit
            },
            {
                name: '单阀时长',
                data: lcg669WebSocket.ckData.index28.data,
                unit: lcg669WebSocket.ckData.index28.unit
            },
            {
                name: '间隔时长',
                data: lcg669WebSocket.ckData.index30.data,
                unit: lcg669WebSocket.ckData.index30.unit
            },
            {
                name: '倒计时',
                data: '-',
                unit: '-'
                // data: lcg669WebSocket.ckData.index32.data,
                // unit: lcg669WebSocket.ckData.index32.unit
            },
        ],
        valve: [
            {
                title: '6号电磁阀',
                onOff: lcg669WebSocket.ckData.index16.data,
                state: lcg669WebSocket.ckData.index8.data,
                controlId: lcg669WebSocket.ckData.index17.id
            },
            {
                title: '7号电磁阀',
                onOff: lcg669WebSocket.ckData.index18.data,
                state: lcg669WebSocket.ckData.index8.data,
                controlId: lcg669WebSocket.ckData.index19.id
            },
            {
                title: '8号电磁阀',
                onOff: lcg669WebSocket.ckData.index20.data,
                state: lcg669WebSocket.ckData.index8.data,
                controlId: lcg669WebSocket.ckData.index21.id
            },
            {
                title: '9号电磁阀',
                onOff: lcg669WebSocket.ckData.index22.data,
                state: lcg669WebSocket.ckData.index8.data,
                controlId: lcg669WebSocket.ckData.index23.id
            },
            {
                title: '10号电磁阀',
                onOff: lcg669WebSocket.ckData.index24.data,
                state: lcg669WebSocket.ckData.index8.data,
                controlId: lcg669WebSocket.ckData.index25.id
            },
        ]
    }
}

onMounted(() => {
    lcg669WebSocket = new LCG669WebSocket('ztWebsocket', 'PN0001' + '/web', assemblyData)
})

onUnmounted(() => {
    lcg669WebSocket.closeWebSocket()
})
</script>

<style lang="less" scoped>
.Control {
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, #DDF6FF 0%, #A1E5FF 100%);
    position: relative;
    padding-top: 120px;
    .title {
        font-weight: 700;
        font-size: 36px;
        color: #1890FF;
        line-height: 52px;
        position: absolute;
        top: 24px;
        left: 50%;
        transform: translateX(-50%);
    }
    .panelBox {
        text-align: center;
        .panel {
            width: 320px;
            height: 320px;
            background-color: white;
            display: inline-block;
            margin-bottom: 40px;
            box-shadow: 0 4px 16px 0 rgba(0, 0, 0, .16);
            border-radius: 12px;
            text-align: left;
            transition: all .5s;
            margin-top: 0;
            .panelTop {
                padding-top: 24px;
                text-align: center;
                vertical-align: top;
                .name, .data {
                    font-size: 16px;
                    line-height: 23px;
                    display: inline-block;
                    margin: 0 6px;
                    font-weight: 700;
                    color: #1890FF;
                }
            }
            .panelButton {
                text-align: center;
                margin-top: 17px;
                .button {
                    padding: 7px 12px;
                    height: auto;
                    font-size: 18px;
                    border-radius: 8px;
                }
            }
            .panelSet {
                padding-top: 24px;

                .panelSetItem {
                    padding-left: 60px;
                    .panelSetName, .panelSetData, .panelSetUnit {
                        display: inline-block;
                        font-size: 16px;
                        line-height: 23px;
                    }
                    .panelSetName {
                        color: #8C8C8C;
                    }
                    .panelSetData {
                        color: #1890FF;
                        font-weight: 700;
                    }
                    .panelSetUnit {
                        color: #1890FF;
                        font-weight: 700;
                    }
                }
            }
            .panelOneClick {
                text-align: center;
                margin-top: 20px;
            }
        }
        .solenoidValveItem {
            vertical-align: top;
            display: inline-block;
            background-color: white;
            margin-left: 12px;
            margin-bottom: 40px;
            width: 240px;
            height: 320px;
            display: inline-block;
            box-shadow: 0 4px 16px 0 rgba(0, 0, 0, .16);
            border-radius: 8px;
            text-align: left;
            transition: all .5s;
            position: relative;
            .solenoidValveTitle {
                font-weight: 700;
                font-size: 24px;
                color: #262626;
                line-height: 23px;
                text-align: center;
                margin-top: 20px;
            }
            .solenoidValveBGC {
                width: 216px;
                height: 244px;
                position: absolute;
                left: 50%;
                transform: translateX(-50%);
                bottom: 12px;
            }
            .solenoidValveRound {
                width: 24px;
                height: 24px;
                border-radius: 50%;
                background-color: #E60E1D;
                position: absolute;
                left: 50%;
                transform: translateX(-50%);
                top: 88px;
                transition: all .5s;
            }
            .solenoidValveRound_c {
                background-color: #1F994B;
            }
            .solenoidValveOnOff {
                width: 160px;
                height: 160px;
                position: absolute;
                left: 50%;
                transform: translateX(-50%) rotate(0);
                bottom: 35px;
                cursor: pointer;
                transition: all .5s;
            }
            .solenoidValveOnOff_c {
                transform: translateX(-50%) rotate(-90deg);
            }
        }
        .solenoidValveItem_c:hover {
            transform: translateY(-10px);
            box-shadow: 0 4px 20px 0 rgba(0, 0, 0, .5);
        }
    }
}
</style>