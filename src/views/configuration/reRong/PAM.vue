<template>
    <YSXK-ScaleDiv>
        <div class="PAM">

            <a-button size="large" type="primary" style="position: absolute; right: 12px; top: 12px;" @click="parameterSetting.isShow = true">{{ parameterSetting.title }}</a-button>

            <YSXK-TimeUpda :position="timeUpda.position" :recentTime="timeUpda.recentTime" />

            <YSXK-DataFrame v-for="(item, i) in aData" :key="i" :position="item.position" :columnData="item.columnData">

                <template v-if="i === 3">
                    <div style="margin-top: 12px;">
                        <a-popconfirm title="确认一键启动？" ok-text="是" cancel-text="否" @confirm="confirmFun('OPEN_CLOSE', item.control_0)">
                            <a-button class="greenButton" style="width: 120px; margin-right: 12px;">一键启动</a-button>
                        </a-popconfirm>
                        <a-popconfirm title="确认一键停止？" ok-text="是" cancel-text="否" @confirm="confirmFun('OPEN_CLOSE', item.control_1)">
                            <a-button class="redButton" style="width: 120px;">一键停止</a-button>
                        </a-popconfirm>
                    </div>
                    <div style="margin-top: 12px;">
                        <a-popconfirm title="确认手动？" ok-text="是" cancel-text="否" @confirm="confirmFun('AUTO', item.control_2)">
                            <a-button class="blueButton" style="width: 120px; margin-right: 12px;">手动</a-button>
                        </a-popconfirm>
                        <a-popconfirm title="确认自动？" ok-text="是" cancel-text="否" @confirm="confirmFun('AUTO', item.control_3)">
                            <a-button class="blueButton" style="width: 120px;">自动</a-button>
                        </a-popconfirm>
                    </div>
                </template>

                <template v-else-if="i >= 4 && i <= 5">
                    <div style="margin-top: 12px; text-align: center;">
                        <a-popconfirm :title="'确认 启动 ' + item.name + ' ？'" ok-text="是" cancel-text="否" @confirm="confirmFun('OPEN_CLOSE', item.control_0)">
                            <a-button class="greenButton" style="margin-right: 12px;">启动</a-button>
                        </a-popconfirm>
                        <a-popconfirm :title="'确认 停止 ' + item.name + ' ？'" ok-text="是" cancel-text="否" @confirm="confirmFun('OPEN_CLOSE', item.control_1)">
                            <a-button class="redButton">停止</a-button>
                        </a-popconfirm>
                    </div>
                </template>

            </YSXK-DataFrame>

            <YSXK-ValveSwitch :modalData="parameterSetting" />

        </div>
    </YSXK-ScaleDiv>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { postSetMonitoringPoint } from '/@/api/engineering'
import type { Confirm } from '../../inspectionCoating/MonitoringPoint.vue'
import LCG669WebSocket from '@/ts/LCG669WebSocket'
import type { WebSocketData } from '/@/ts/publicData'

import feederMachine1_green from '/@/assets/images/configuration/reRong/feederMachine1_green.png'
import feederMachine1_orange from '/@/assets/images/configuration/reRong/feederMachine1_orange.png'

import bobbingMachine1_green from '/@/assets/images/configuration/reRong/bobbingMachine1_green.png'
// import bobbingMachine1_orange from '/@/assets/images/configuration/reRong/bobbingMachine1_orange.png'

import blender1_green from '/@/assets/images/configuration/reRong/blender1_green.png'
import blender1_orange from '/@/assets/images/configuration/reRong/blender1_orange.png'

import blender2_green from '/@/assets/images/configuration/reRong/blender2_green.png'
import blender2_orange from '/@/assets/images/configuration/reRong/blender2_orange.png'

import blender3_green from '/@/assets/images/configuration/reRong/blender3_green.png'
import blender3_orange from '/@/assets/images/configuration/reRong/blender3_orange.png'

import screwPump1_green from '/@/assets/images/configuration/reRong/screwPump1_green.png'
import screwPump1_orange from '/@/assets/images/configuration/reRong/screwPump1_orange.png'

import screwPump2_green from '/@/assets/images/configuration/reRong/screwPump2_green.png'
import screwPump2_orange from '/@/assets/images/configuration/reRong/screwPump2_orange.png'

import YSXKValveSwitch from '/@/components/YSXK-ValveSwitch.vue'

import liquidLevel_green from '/@/assets/images/configuration/reRong/liquidLevel_green.png'

let lcg669WebSocket: LCG669WebSocket<WebSocketData>

const parameterSetting = reactive({
    title: '参数设置',
    isShow: false,
    arrData: [],
    inputOBJ: {
        input29: 0,
        input34: 0,
        input39: 0,
    }
})

/**
 * 气泡确认框 是
 */
const confirmFun = (opType: Confirm['opType'], varId: string, value?: string) => {
    postSetMonitoringPoint({
        opType,
        varId,
        value
    })
    .then(e => {

    })
}

const timeUpda = reactive({
    position: {
        top: '12px',
        left: '12px'
    },
    recentTime: ''
})

const aData = ref()

const assemblyData = () => {
    timeUpda.recentTime = lcg669WebSocket.ckData.recentTime

    aData.value = [
        // 0 进水阀
        {
            // 数据框位置
            position: {
                top: '562px',
                left: '34px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '进水阀', // 标题
                    data: lcg669WebSocket.ckData.index4.data, // 值
                    type: 'Hoist', // 数据类型
                },
                {
                    name: '流量', // 标题
                    data: lcg669WebSocket.ckData.index5.data, // 值
                    unit: lcg669WebSocket.ckData.index5.unit, // 单位
                    flag: lcg669WebSocket.ckData.index5.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
                {
                    name: '流量低报警', // 标题
                    data: lcg669WebSocket.ckData.index57.data, // 值
                    type: 'StressState', // 数据类型
                },
            ],
        },
        // 1 给料机
        {
            // 数据框位置
            position: {
                top: '252px',
                left: '365px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '给料机', // 标题
                    data : lcg669WebSocket.ckData.index6.data, // 值
                    dataX: lcg669WebSocket.ckData.index7.data, // 值
                    type: 'AgitatorState', // 数据类型
                    img: feederMachine1_green,
                    imgX: feederMachine1_orange,
                    // 图片位置
                    location: {
                        top: '363px',
                        left: '240px'
                    },
                    // 图片宽高
                    size: {
                        w: '72px',
                        h: '80px'
                    }
                },
                {
                    name: '振动机', // 标题
                    data: lcg669WebSocket.ckData.index8.data, // 值
                    type: 'AgitatorState', // 数据类型
                    img: bobbingMachine1_green,
                    // 图片位置
                    location: {
                        top: '351px',
                        left: '284px'
                    },
                    // 图片宽高
                    size: {
                        w: '32px',
                        h: '40px'
                    }
                },
                {
                    name: '频率', // 标题
                    data: lcg669WebSocket.ckData.index9.data, // 值
                    unit: lcg669WebSocket.ckData.index9.unit, // 单位
                    flag: lcg669WebSocket.ckData.index9.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
                {
                    name: '给料量', // 标题
                    data: lcg669WebSocket.ckData.index10.data, // 值
                    unit: lcg669WebSocket.ckData.index10.unit, // 单位
                    flag: lcg669WebSocket.ckData.index10.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
                {
                    name: '低料位报警', // 标题
                    data: lcg669WebSocket.ckData.index58.data, // 值
                    type: 'StressState', // 数据类型
                },
            ],
        },
        // 2 给料箱
        {
            // 数据框位置
            position: {
                top: '562px',
                left: '384px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '1#搅拌机', // 标题
                    data : lcg669WebSocket.ckData.index11.data, // 值
                    dataX: lcg669WebSocket.ckData.index12.data, // 值
                    type: 'AgitatorState', // 数据类型
                    img: blender1_green,
                    imgX: blender1_orange,
                    // 图片位置
                    location: {
                        top: '429px',
                        left: '291px'
                    },
                    // 图片宽高
                    size: {
                        w: '80px',
                        h: '120px'
                    }
                },
                {
                    name: '2#搅拌机', // 标题
                    data : lcg669WebSocket.ckData.index13.data, // 值
                    dataX: lcg669WebSocket.ckData.index14.data, // 值
                    type: 'AgitatorState', // 数据类型
                    img: blender2_green,
                    imgX: blender2_orange,
                    // 图片位置
                    location: {
                        top: '429px',
                        left: '424px'
                    },
                    // 图片宽高
                    size: {
                        w: '80px',
                        h: '120px'
                    }
                },
                {
                    name: '3#搅拌机', // 标题
                    data : lcg669WebSocket.ckData.index15.data, // 值
                    dataX: lcg669WebSocket.ckData.index16.data, // 值
                    type: 'AgitatorState', // 数据类型
                    img: blender3_green,
                    imgX: blender3_orange,
                    // 图片位置
                    location: {
                        top: '429px',
                        left: '557px'
                    },
                    // 图片宽高
                    size: {
                        w: '80px',
                        h: '120px'
                    }
                },
                {
                    name: '液位', // 标题
                    data: lcg669WebSocket.ckData.index17.data, // 值
                    unit: lcg669WebSocket.ckData.index17.unit, // 单位
                    flag: lcg669WebSocket.ckData.index17.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
            ],
        },
        // 3 控制
        {
            // 数据框位置
            position: {
                top: '147px',
                left: '753px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '控制模式', // 标题
                    data : lcg669WebSocket.ckData.index2.data, // 值
                    data2: 'true', // 值
                    type: 'AgitatorMode', // 数据类型
                },
                {
                    name: '配药浓度', // 标题
                    data: lcg669WebSocket.ckData.index3.data, // 值
                    unit: lcg669WebSocket.ckData.index3.unit, // 单位
                    flag: lcg669WebSocket.ckData.index3.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
            ],
            control_0: lcg669WebSocket.ckData.index24.id,
            control_1: lcg669WebSocket.ckData.index24.id,
            control_2: lcg669WebSocket.ckData.index25.id,
            control_3: lcg669WebSocket.ckData.index25.id,
        },
        // 4 1#螺杆泵
        {
            // 数据框位置
            position: {
                top: '290px',
                left: '1050px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '运行状态', // 标题
                    data : lcg669WebSocket.ckData.index18.data, // 值
                    dataX: lcg669WebSocket.ckData.index20.data, // 值
                    type: 'AgitatorState', // 数据类型
                    img: screwPump1_green,
                    imgX: screwPump1_orange,
                    // 图片位置
                    location: {
                        top: '439px',
                        left: '961px'
                    },
                    // 图片宽高
                    size: {
                        w: '320px',
                        h: '96px'
                    }
                },
                {
                    name: '频率', // 标题
                    data: lcg669WebSocket.ckData.index19.data, // 值
                    unit: lcg669WebSocket.ckData.index19.unit, // 单位
                    flag: lcg669WebSocket.ckData.index19.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
            ],
            control_0: lcg669WebSocket.ckData.index31.id,
            control_1: lcg669WebSocket.ckData.index32.id,
            name: '1#螺杆泵'
        },
        // 5 2#螺杆泵
        {
            // 数据框位置
            position: {
                top: '590px',
                left: '1050px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '运行状态', // 标题
                    data : lcg669WebSocket.ckData.index21.data, // 值
                    dataX: lcg669WebSocket.ckData.index22.data, // 值
                    type: 'AgitatorState', // 数据类型
                    img: screwPump2_green,
                    imgX: screwPump2_orange,
                    // 图片位置
                    location: {
                        top: '735px',
                        left: '982px'
                    },
                    // 图片宽高
                    size: {
                        w: '320px',
                        h: '96px'
                    }
                },
                {
                    name: '频率', // 标题
                    data: lcg669WebSocket.ckData.index23.data, // 值
                    unit: lcg669WebSocket.ckData.index23.unit, // 单位
                    flag: lcg669WebSocket.ckData.index23.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
            ],
            control_0: lcg669WebSocket.ckData.index36.id,
            control_1: lcg669WebSocket.ckData.index37.id,
            name: '2#螺杆泵'
        },

        // 所有设备状态
        {
            // 数据框位置
            position: {
                top : '-999px',
                left: '-999px'
            },
            // 每一条数据
            columnData: [
                // 储药箱 高液位
                {
                    data: lcg669WebSocket.ckData.index41.data, // 值
                    type: 'ImgReverse', // 数据类型
                    img: liquidLevel_green,
                    // 图片位置
                    location: {
                        top: '656px',
                        left: '616px'
                    },
                    // 图片宽高
                    size: {
                        w: '42px',
                        h: '12px'
                    }
                },
                // 储药箱 中液位
                {
                    data: lcg669WebSocket.ckData.index42.data, // 值
                    type: 'ImgReverse', // 数据类型
                    img: liquidLevel_green,
                    // 图片位置
                    location: {
                        top: '713px',
                        left: '616px'
                    },
                    // 图片宽高
                    size: {
                        w: '42px',
                        h: '12px'
                    }
                },
                // 储药箱 低液位
                {
                    data: lcg669WebSocket.ckData.index43.data, // 值
                    type: 'ImgReverse', // 数据类型
                    img: liquidLevel_green,
                    // 图片位置
                    location: {
                        top: '774px',
                        left: '616px'
                    },
                    // 图片宽高
                    size: {
                        w: '42px',
                        h: '12px'
                    }
                },
            ],
        },
    ]

    parameterSetting.arrData = [
        {
            // 类型
            type  : 'frequency',
            // 名称
            name  : '配药浓度',
            // 当前频率
            data  : lcg669WebSocket.ckData.index28.data,
            unit  : lcg669WebSocket.ckData.index28.unit,
            control_0: lcg669WebSocket.ckData.index29.id,
            inputName: 'input29'
        },
        {
            // 类型
            type  : 'frequency',
            // 名称
            name  : '1#泵频率',
            // 当前频率
            data  : lcg669WebSocket.ckData.index33.data,
            unit  : lcg669WebSocket.ckData.index33.unit,
            control_0: lcg669WebSocket.ckData.index34.id,
            inputName: 'input34'
        },
        {
            // 类型
            type  : 'frequency',
            // 名称
            name  : '2#泵频率',
            // 当前频率
            data  : lcg669WebSocket.ckData.index38.data,
            unit  : lcg669WebSocket.ckData.index38.unit,
            control_0: lcg669WebSocket.ckData.index39.id,
            inputName: 'input39'
        },
    ]
}

onMounted(() => {
    lcg669WebSocket = new LCG669WebSocket('ztWebsocket', 'PAM0001' + '/web', assemblyData)
})

onUnmounted(() => {
    lcg669WebSocket.closeWebSocket()
})
</script>

<style lang="less" scoped>
.PAM {
    width: 100%;
    height: 100%;
    background-image: url(/src/assets/images/configuration/reRong/pamBGC.png);
    background-size: 1688px 854px;
    background-repeat: no-repeat;
}
</style>