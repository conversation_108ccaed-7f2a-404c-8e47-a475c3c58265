// NaOCl 次氯酸钠

<template>
<YSXK-ScaleDiv>
    <div class="NaOCl">
        
        <YSXK-TimeUpda
            :position  ="timeUpda.position"
            :recentTime="timeUpda.recentTime"
        />

        <YSXK-DataFrame
            v-for="(item, i) in aData"
            :key="i"
            :position="item.position"
            :columnData="item.columnData"
        >
        </YSXK-DataFrame>

    </div>
</YSXK-ScaleDiv>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { postSetMonitoringPoint } from '/@/api/engineering'
import type { Confirm } from '../../inspectionCoating/MonitoringPoint.vue'
import LCG669WebSocket from '@/ts/LCG669WebSocket'
import type { WebSocketData } from '/@/ts/publicData'

import liquidLevel_green from '/@/assets/images/configuration/reRong/liquidLevel_green.png'

import draughtFan_green  from '/@/assets/images/configuration/reRong/draughtFan_green.png'
import draughtFan_orange from '/@/assets/images/configuration/reRong/draughtFan_orange.png'

import dosingPump_green  from '/@/assets/images/configuration/reRong/dosingPump_green.png'
import dosingPump_orange from '/@/assets/images/configuration/reRong/dosingPump_orange.png'

// import electricValve_gray   from '/@/assets/images/monitoringPoint/electricValve_gray.png'
import electricValve_green  from '/@/assets/images/monitoringPoint/electricValve_green.png'
import electricValve_orange from '/@/assets/images/monitoringPoint/electricValve_orange.png'

let lcg669WebSocket: LCG669WebSocket<WebSocketData>

const timeUpda = reactive({
    position: {
        top: '12px',
        left: '12px'
    },
    recentTime: ''
})

const aData = ref()

const assemblyData = () => {
    timeUpda.recentTime = lcg669WebSocket.ckData.recentTime

    // console.log('lcg669WebSocket.ckData.index13.data值：', lcg669WebSocket.ckData.index13.data)

    aData.value = [
        // 数据
        {
            // 数据框位置
            position: {
                top: '382px',
                left: '12px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '出药温度', // 标题
                    data: lcg669WebSocket.ckData.index1.data, // 值
                    unit: lcg669WebSocket.ckData.index1.unit, // 单位
                    flag: lcg669WebSocket.ckData.index1.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
                {
                    name: '余氯', // 标题
                    data: lcg669WebSocket.ckData.index2.data, // 值
                    unit: lcg669WebSocket.ckData.index2.unit, // 单位
                    flag: lcg669WebSocket.ckData.index2.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
                {
                    name: '原水流量', // 标题
                    data: lcg669WebSocket.ckData.index3.data, // 值
                    unit: lcg669WebSocket.ckData.index3.unit, // 单位
                    flag: lcg669WebSocket.ckData.index3.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
            ],
        },
        // 报警
        {
            // 数据框位置
            position: {
                top: '495px',
                left: '12px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '自来水压力', // 标题
                    data: lcg669WebSocket.ckData.index23.data, // 值
                    type: 'StressState', // 数据类型
                },
                {
                    name: '软水器再生', // 标题
                    data: lcg669WebSocket.ckData.index24.data, // 值
                    type: 'StressState', // 数据类型
                },
                {
                    name: '排氢风压', // 标题
                    data: lcg669WebSocket.ckData.index26.data, // 值
                    type: 'StressState', // 数据类型
                },
                {
                    name: '氢气报警', // 标题
                    data: lcg669WebSocket.ckData.index27.data, // 值
                    type: 'StressState', // 数据类型
                },
                {
                    name: '电解槽液位', // 标题
                    data: lcg669WebSocket.ckData.index28.data, // 值
                    type: 'StressState', // 数据类型
                },
                {
                    name: '电解槽压力', // 标题
                    data: lcg669WebSocket.ckData.index29.data, // 值
                    type: 'StressState', // 数据类型
                },
                {
                    name: '设备供水压力不足', // 标题
                    data: lcg669WebSocket.ckData.index30.data, // 值
                    type: 'StressState', // 数据类型
                },
                {
                    name: '室内氢气浓度超标', // 标题
                    data: lcg669WebSocket.ckData.index31.data, // 值
                    type: 'StressState', // 数据类型
                },
                {
                    name: '储药罐液位低', // 标题
                    data: lcg669WebSocket.ckData.index32.data, // 值
                    type: 'StressState', // 数据类型
                },
                {
                    name: '投加泵故障', // 标题
                    data: lcg669WebSocket.ckData.index33.data, // 值
                    type: 'StressState', // 数据类型
                },
                {
                    name: '远程指示', // 标题
                    data: lcg669WebSocket.ckData.index34.data, // 值
                    type: 'StressState', // 数据类型
                },
            ],
        },
        // 所有设备状态
        {
            // 数据框位置
            position: {
                top : '-999px',
                left: '-999px'
            },
            // 每一条数据
            columnData: [
                // 软水箱 液位
                {
                    data: lcg669WebSocket.ckData.index13.data, // 值
                    type: 'ImgReverse', // 数据类型
                    img: liquidLevel_green,
                    // 图片位置
                    location: {
                        top: '467px',
                        left: '415px'
                    },
                    // 图片宽高
                    size: {
                        w: '42px',
                        h: '12px'
                    }
                },
                // 储药箱 高液位
                {
                    data: lcg669WebSocket.ckData.index6.data, // 值
                    type: 'ImgReverse', // 数据类型
                    img: liquidLevel_green,
                    // 图片位置
                    location: {
                        top: '268px',
                        left: '1065px'
                    },
                    // 图片宽高
                    size: {
                        w: '42px',
                        h: '12px'
                    }
                },
                // 储药箱 中液位
                {
                    data: lcg669WebSocket.ckData.index7.data, // 值
                    type: 'ImgReverse', // 数据类型
                    img: liquidLevel_green,
                    // 图片位置
                    location: {
                        top: '339px',
                        left: '1065px'
                    },
                    // 图片宽高
                    size: {
                        w: '42px',
                        h: '12px'
                    }
                },
                // 储药箱 低液位
                {
                    data: lcg669WebSocket.ckData.index8.data, // 值
                    type: 'ImgReverse', // 数据类型
                    img: liquidLevel_green,
                    // 图片位置
                    location: {
                        top: '374px',
                        left: '1065px'
                    },
                    // 图片宽高
                    size: {
                        w: '42px',
                        h: '12px'
                    }
                },
                // 酸洗箱 液位
                {
                    data: lcg669WebSocket.ckData.index16.data, // 值
                    type: 'ImgReverse', // 数据类型
                    img: liquidLevel_green,
                    // 图片位置
                    location: {
                        top: '712px',
                        left: '1554px'
                    },
                    // 图片宽高
                    size: {
                        w: '42px',
                        h: '12px'
                    }
                },
                
                // 稀释水泵
                {
                    data: lcg669WebSocket.ckData.index14.data, // 值
                    type: 'Img', // 数据类型
                    img: dosingPump_green,
                    // 图片位置
                    location: {
                        top: '422px',
                        left: '508px'
                    },
                    // 图片宽高
                    size: {
                        w: '74px',
                        h: '96px'
                    }
                },
                // // 浓盐水泵
                // {
                //     data: 'true', // 值
                //     type: 'Img', // 数据类型
                //     img: dosingPump_green,
                //     imgX: dosingPump_orange,
                //     // 图片位置
                //     location: {
                //         top: '680px',
                //         left: '484px'
                //     },
                //     // 图片宽高
                //     size: {
                //         w: '74px',
                //         h: '96px'
                //     }
                // },
                // // 酸洗泵
                // {
                //     data: 'true', // 值
                //     type: 'Img', // 数据类型
                //     img: dosingPump_green,
                //     imgX: dosingPump_orange,
                //     // 图片位置
                //     location: {
                //         top: '620px',
                //         left: '1247px'
                //     },
                //     // 图片宽高
                //     size: {
                //         w: '74px',
                //         h: '96px'
                //     }
                // },
                // 1#投加泵
                {
                    data : lcg669WebSocket.ckData.index11.data, // 值
                    dataX: lcg669WebSocket.ckData.index12.data, // 值
                    type: 'Img', // 数据类型
                    img: dosingPump_green,
                    imgX: dosingPump_orange,
                    // 图片位置
                    location: {
                        top: '330px',
                        left: '1507px'
                    },
                    // 图片宽高
                    size: {
                        w: '74px',
                        h: '96px'
                    }
                },
                // 2#投加泵
                {
                    data : lcg669WebSocket.ckData.index9.data, // 值
                    dataX: lcg669WebSocket.ckData.index10.data, // 值
                    type: 'Img', // 数据类型
                    img: dosingPump_green,
                    imgX: dosingPump_orange,
                    // 图片位置
                    location: {
                        top: '155px',
                        left: '1507px'
                    },
                    // 图片宽高
                    size: {
                        w: '74px',
                        h: '96px'
                    }
                },
                
                // 1#排氢风机
                {
                    data: lcg669WebSocket.ckData.index4.data, // 值
                    type: 'Img', // 数据类型
                    img: draughtFan_green,
                    // 图片位置
                    location: {
                        top: '60px',
                        left: '1339px'
                    },
                    // 图片宽高
                    size: {
                        w: '90px',
                        h: '96px'
                    }
                },
                // 2#排氢风机
                {
                    data: lcg669WebSocket.ckData.index5.data, // 值
                    type: 'Img', // 数据类型
                    img: draughtFan_green,
                    // 图片位置
                    location: {
                        top: '179px',
                        left: '1205px'
                    },
                    // 图片宽高
                    size: {
                        w: '90px',
                        h: '96px'
                    }
                },

                // 稀释水阀
                {
                    data: lcg669WebSocket.ckData.index15.data, // 值
                    type: 'Img', // 数据类型
                    img: electricValve_green,
                    // 图片位置
                    location: {
                        top: '444px',
                        left: '619px'
                    },
                    // 图片宽高
                    size: {
                        w: '52px',
                        h: '64px'
                    }
                },
            ],
        },
    ]
}

onMounted(() => {
    lcg669WebSocket = new LCG669WebSocket('ztWebsocket', 'XD0001' + '/web', assemblyData)
})

onUnmounted(() => {
    lcg669WebSocket.closeWebSocket()
})
</script>

<style lang="less" scoped>
.NaOCl {
    width: 100%;
    height: 100%;
    background-image: url(/src/assets/images/configuration/reRong/NaOClBGC.png);
    background-size: 1688px 854px;
    background-repeat: no-repeat;
}
</style>