<template>
<YSXK-ScaleDiv>
    <div class="Configuration">
        <router-link class="jump jump0" to="/configuration/reRong/control"></router-link>
        <router-link class="jump jump1" to="/configuration/reRong/control"></router-link>
        <router-link class="jump jump2" to="/configuration/reRong/reuseBasin"></router-link>
        <router-link class="jump jump3" to="/configuration/reRong/pumpStation"></router-link>
        <router-link class="jump jump4" to="/configuration/reRong/pac"></router-link>
        <router-link class="jump jump5" to="/configuration/reRong/pam"></router-link>
        <router-link class="jump jump6" to="/configuration/reRong/NaOCl"></router-link>

        <YSXK-TimeUpda
            :position  ="timeUpda.position"
            :recentTime="timeUpda.recentTime"
        />

        <YSXK-DataFrame
            v-for="(item, i) in aData"
            :key="i"
            :position="item.position"
            :columnData="item.columnData"
        >
            <div v-if     ="i === 7 || i === 8" style="text-align: center;">
                <div style="margin-top: 12px;">
                    <a-popconfirm :title="'确认将 ' + item.name + ' 设置为 打开？'" ok-text="是" cancel-text="否" @confirm="confirmFun('OPEN', item.control_0)">
                        <a-button class="greenButton" style="margin-right: 12px;">打开</a-button>
                    </a-popconfirm>
                    <a-popconfirm :title="'确认将 ' + item.name + ' 设置为 关闭？'" ok-text="是" cancel-text="否" @confirm="confirmFun('CLOSE', item.control_1)">
                        <a-button class="redButton">关闭</a-button>
                    </a-popconfirm>
                </div>
                <div style="margin-top: 12px;">
                    <a-popconfirm :title="'确认将 ' + item.name + ' 设置为 停止？'" ok-text="是" cancel-text="否" @confirm="confirmFun('STOP', item.control_2)">
                        <a-button class="blueButton" style="margin-right: 12px;">停止</a-button>
                    </a-popconfirm>
                    <a-popconfirm :title="'确认将 ' + item.name + ' 设置为 复位？'" ok-text="是" cancel-text="否" @confirm="confirmFun('RESET_FAULT', item.control_3)">
                        <a-button class="blueButton">复位</a-button>
                    </a-popconfirm>
                </div>
            </div>

            <div v-if     ="i === 10 || i === 11" style="text-align: center;">
                <div style="margin-top: 12px;">
                    <a-popconfirm :title="'确认将 ' + item.name + ' 设置为 手动？'" ok-text="是" cancel-text="否" @confirm="confirmFun('STOP', item.control_2)">
                        <a-button class="blueButton" style="margin-right: 12px;">手动</a-button>
                    </a-popconfirm>
                    <a-popconfirm :title="'确认将 ' + item.name + ' 设置为 自动？'" ok-text="是" cancel-text="否" @confirm="confirmFun('RESET_FAULT', item.control_3)">
                        <a-button class="blueButton">自动</a-button>
                    </a-popconfirm>
                </div>
            </div>
        </YSXK-DataFrame>
    </div>
</YSXK-ScaleDiv>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { postSetMonitoringPoint } from '/@/api/engineering'
import type { Confirm } from '../../inspectionCoating/MonitoringPoint.vue'
import electricValve_gray   from '/@/assets/images/monitoringPoint/electricValve_gray.png'
import electricValve_green  from '/@/assets/images/monitoringPoint/electricValve_green.png'
import electricValve_orange from '/@/assets/images/monitoringPoint/electricValve_orange.png'
import LCG669WebSocket from '@/ts/LCG669WebSocket'
import type { WebSocketData } from '/@/ts/publicData'

let lcg669WebSocket: LCG669WebSocket<WebSocketData>

const aData = ref([])

const assemblyData = () => {
    // console.log('lcg669WebSocket.ckData.index5.data当前值：', lcg669WebSocket.ckData.index5.data)

    timeUpda.recentTime = lcg669WebSocket.ckData.recentTime

    aData.value = [
        // 0 进水
        {
            // 数据框位置
            position: {
                top: '370px',
                left: '12px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '流量', // 标题
                    data: lcg669WebSocket.ckData.index2.data, // 值
                    unit: lcg669WebSocket.ckData.index2.unit, // 单位
                    flag: lcg669WebSocket.ckData.index2.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
            ],
        },
        // 1 当日 && 总进出
        {
            // 数据框位置
            position: {
                top: '712px',
                left: '12px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '当日进水量', // 标题
                    data: lcg669WebSocket.ckData.index24.data, // 值
                    unit: lcg669WebSocket.ckData.index24.unit, // 单位
                    flag: lcg669WebSocket.ckData.index24.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
                {
                    name: '当日出水量', // 标题
                    data: lcg669WebSocket.ckData.index25.data, // 值
                    unit: lcg669WebSocket.ckData.index25.unit, // 单位
                    flag: lcg669WebSocket.ckData.index25.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
                {
                    name: '总进水量', // 标题
                    data: lcg669WebSocket.ckData.index26.data, // 值
                    unit: lcg669WebSocket.ckData.index26.unit, // 单位
                    flag: lcg669WebSocket.ckData.index26.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
                {
                    name: '总出水量', // 标题
                    data: lcg669WebSocket.ckData.index27.data, // 值
                    unit: lcg669WebSocket.ckData.index27.unit, // 单位
                    flag: lcg669WebSocket.ckData.index27.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
            ],
        },
        // 2 配水井
        {
            // 数据框位置
            position: {
                top: '270px',
                left: '234px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '浊度', // 标题
                    data: lcg669WebSocket.ckData.index3.data, // 值
                    unit: lcg669WebSocket.ckData.index3.unit, // 单位
                    flag: lcg669WebSocket.ckData.index3.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
                {
                    name: 'PH', // 标题
                    data: lcg669WebSocket.ckData.index4.data, // 值
                    unit: lcg669WebSocket.ckData.index4.unit, // 单位
                    flag: lcg669WebSocket.ckData.index4.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
            ],
        },
        // 3 1#沉淀池
        {
            // 数据框位置
            position: {
                top: '120px',
                left: '550px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '排泥状态', // 标题
                    data: lcg669WebSocket.ckData.index5.data, // 值
                    type: 'SludgeDischargeState', // 数据类型
                },
                {
                    name: '排泥模式', // 标题
                    data: lcg669WebSocket.ckData.index6.data, // 值
                    type: 'SludgeDischargeMode', // 数据类型
                },
            ],
        },
        // 4 2#沉淀池
        {
            // 数据框位置
            position: {
                top: '410px',
                left: '550px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '排泥状态', // 标题
                    data: lcg669WebSocket.ckData.index7.data, // 值
                    type: 'SludgeDischargeState', // 数据类型
                },
                {
                    name: '排泥模式', // 标题
                    data: lcg669WebSocket.ckData.index8.data, // 值
                    type: 'SludgeDischargeMode', // 数据类型
                },
            ],
        },
        // 5 回用水池
        {
            // 数据框位置
            position: {
                top: '671px',
                left: '861px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '液位', // 标题
                    data: lcg669WebSocket.ckData.index1.data, // 值
                    unit: lcg669WebSocket.ckData.index1.unit, // 单位
                    flag: lcg669WebSocket.ckData.index1.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
            ],
        },
        // 6 清水池
        {
            // 数据框位置
            position: {
                top: '200px',
                left: '1177px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '液位1', // 标题
                    data: lcg669WebSocket.ckData.index12.data, // 值
                    unit: lcg669WebSocket.ckData.index12.unit, // 单位
                    flag: lcg669WebSocket.ckData.index12.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
                {
                    name: '液位2', // 标题
                    data: lcg669WebSocket.ckData.index13.data, // 值
                    unit: lcg669WebSocket.ckData.index13.unit, // 单位
                    flag: lcg669WebSocket.ckData.index13.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
            ],
        },
        // 7 1#出水
        {
            // 数据框位置
            position: {
                top: '80px',
                left: '1413px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '1#流量', // 标题
                    data: lcg669WebSocket.ckData.index15.data, // 值
                    unit: lcg669WebSocket.ckData.index15.unit, // 单位
                    flag: lcg669WebSocket.ckData.index15.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
                {
                    name: '1#阀门', // 标题
                    data: lcg669WebSocket.ckData.index14.data, // 值
                    dataX: lcg669WebSocket.ckData.index18.data, // 值
                    unit: lcg669WebSocket.ckData.index14.unit, // 单位
                    type: 'Hoist', // 数据类型
                    img: electricValve_green,
                    imgX: electricValve_orange,
                    imgX2: electricValve_gray,
                    // 图片位置
                    location: {
                        top: '294px',
                        left: '1497px'
                    },
                    // 图片宽高
                    size: {
                        w: '31px',
                        h: '40px'
                    }
                },
            ],
            // 控制面板按钮id 启动 停止 手动 自动
            control_0: lcg669WebSocket.ckData.index31.id,
            control_1: lcg669WebSocket.ckData.index32.id,
            control_2: lcg669WebSocket.ckData.index33.id,
            control_3: lcg669WebSocket.ckData.index34.id,
            name: '清水池 1#出水阀门'
        },
        // 8 2#出水
        {
            // 数据框位置
            position: {
                top: '355px',
                left: '1413px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '2#流量', // 标题
                    data: lcg669WebSocket.ckData.index17.data, // 值
                    unit: lcg669WebSocket.ckData.index17.unit, // 单位
                    flag: lcg669WebSocket.ckData.index17.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
                {
                    name: '2#阀门', // 标题
                    data: lcg669WebSocket.ckData.index16.data, // 值
                    dataX: lcg669WebSocket.ckData.index19.data, // 值
                    unit: lcg669WebSocket.ckData.index16.unit, // 单位
                    type: 'Hoist', // 数据类型
                    img: electricValve_green,
                    imgX: electricValve_orange,
                    imgX2: electricValve_gray,
                    // 图片位置
                    location: {
                        top: '567px',
                        left: '1497px'
                    },
                    // 图片宽高
                    size: {
                        w: '31px',
                        h: '40px'
                    }
                },
            ],
            // 控制面板按钮id 启动 停止 手动 自动
            control_0: lcg669WebSocket.ckData.index41.id,
            control_1: lcg669WebSocket.ckData.index42.id,
            control_2: lcg669WebSocket.ckData.index43.id,
            control_3: lcg669WebSocket.ckData.index44.id,
            name: '清水池 2#出水阀门'
        },
        // 9 出水水质检测
        {
            // 数据框位置
            position: {
                top: '615px',
                left: '1496px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '浊度', // 标题
                    data: lcg669WebSocket.ckData.index9.data, // 值
                    unit: lcg669WebSocket.ckData.index9.unit, // 单位
                    flag: lcg669WebSocket.ckData.index9.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
                {
                    name: '余氯', // 标题
                    data: lcg669WebSocket.ckData.index10.data, // 值
                    unit: lcg669WebSocket.ckData.index10.unit, // 单位
                    flag: lcg669WebSocket.ckData.index10.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
                {
                    name: 'PH', // 标题
                    data: lcg669WebSocket.ckData.index11.data, // 值
                    unit: lcg669WebSocket.ckData.index11.unit, // 单位
                    flag: lcg669WebSocket.ckData.index11.warningFlag, // 是否报警
                    type: '', // 数据类型
                },
            ],
        },
        // 10 1#出水阀模式
        {
            // 数据框位置
            position: {
                top: '70px',
                left: '1068px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '1#出水阀模式', // 标题
                    data: lcg669WebSocket.ckData.index37.data, // 值
                    type: 'SludgeDischargeMode', // 数据类型
                },
            ],
            // 控制面板按钮id 启动 停止 手动 自动
            control_0: lcg669WebSocket.ckData.index37.id,
            control_1: lcg669WebSocket.ckData.index37.id,
            name: '清水池 1#出水阀模式'
        },
        // 10 2#出水阀模式
        {
            // 数据框位置
            position: {
                top: '600px',
                left: '1068px'
            },
            // 每一条数据
            columnData: [
                {
                    name: '2#出水阀模式', // 标题
                    data: lcg669WebSocket.ckData.index47.data, // 值
                    type: 'SludgeDischargeMode', // 数据类型
                },
            ],
            // 控制面板按钮id 启动 停止 手动 自动
            control_0: lcg669WebSocket.ckData.index47.id,
            control_1: lcg669WebSocket.ckData.index47.id,
            name: '清水池 2#出水阀模式'
        },
    ]
}

onMounted(() => {
    lcg669WebSocket = new LCG669WebSocket('ztWebsocket', 'WATER001' + '/web', assemblyData)
})

onUnmounted(() => {
    lcg669WebSocket.closeWebSocket()
})

/**
 * 气泡确认框 是
 */
const confirmFun = (opType: Confirm['opType'], varId: string, value?: string) => {
    postSetMonitoringPoint({
        opType,
        varId,
        value
    })
    .then(e => {

    })
}

const timeUpda = reactive({
    position: {
        top: '12px',
        left: '12px'
    },
    recentTime: ''
})
</script>

<style lang="less" scoped>
.Configuration {
    width: 100%;
    height: 100%;
    background-image: url(/src/assets/images/configuration/reRong/configurationBGC.png);
    background-size: 1688px 854px;
    background-repeat: no-repeat;
    .jump {
        position: absolute;
        transition: all .2669s;
        border-radius: 12.5%;
    }
    .jump:hover {
        box-shadow: rgba(100, 100, 111, .5) 0 7px 29px 0;
    }
    .jump0 {
        width: 350px;
        height: 200px;
        top: 200px;
        left: 450px;
    }
    .jump1 {
        width: 350px;
        height: 200px;
        top: 490px;
        left: 450px;
    }
    .jump2 {
        width: 270px;
        height: 180px;
        top: 625px;
        left: 788px;
    }
    .jump3 {
        width: 175px;
        height: 110px;
        top: 717px;
        left: 1310px;
    }
    .jump4 {
        width: 220px;
        height: 56px;
        top: 113px;
        left: 27px;
    }
    .jump5 {
        width: 220px;
        height: 56px;
        top: 172px;
        left: 27px;
    }
    .jump6 {
        width: 162px;
        height: 62px;
        top: 70px;
        left: 247px;
    }
}
</style>

<style lang="less">
.is-disabled {
    background: #ccc !important;
    color: gray !important;
}
.is-disabled:hover {
    color: gray !important;
}
</style>