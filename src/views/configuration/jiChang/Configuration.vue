<template>
<YSXK-ScaleDiv>
    <div class="Configuration">
    </div>
</YSXK-ScaleDiv>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { postSetMonitoringPoint } from '/@/api/engineering'
import type { Confirm } from '../../inspectionCoating/MonitoringPoint.vue'
import electricValve_gray   from '/@/assets/images/monitoringPoint/electricValve_gray.png'
import electricValve_green  from '/@/assets/images/monitoringPoint/electricValve_green.png'
import electricValve_orange from '/@/assets/images/monitoringPoint/electricValve_orange.png'

/**
 * 气泡确认框 是
 */
const confirmFun = (opType: Confirm['opType'], varId: string, value?: string) => {
    postSetMonitoringPoint({
        opType,
        varId
    })
    .then(e => {

    })
}

const timeUpda = reactive({
    position: {
        top: '12px',
        left: '12px'
    },
    recentTime: '2024-9-10 10:31'
})

const aData = ref([
    // 0 进水流量
    {
        // 数据框位置
        position: {
            top: '318px',
            left: '108px'
        },
        // 每一条数据
        columnData: [
            {
                name: '进水流量', // 标题
                data: '123.12', // 值
                unit: 'm³/h', // 单位
                flag: '1', // 是否报警
                type: '', // 数据类型
            },
        ],
    },
    // 1 阀门
    {
        // 数据框位置
        position: {
            top: '194px',
            left: '443px'
        },
        // 每一条数据
        columnData: [
            {
                name: '阀门', // 标题
                data: '1', // 值
                type: 'ValveState', // 数据类型
                img: electricValve_green,
                imgX: electricValve_orange,
                imgX2: electricValve_gray,
                // 图片位置
                location: {
                    top: '385px',
                    left: '468px'
                },
                // 图片宽高
                size: {
                    w: '97px',
                    h: '120px'
                }
            },
        ],
    },
    // 2 液位
    {
        // 数据框位置
        position: {
            top: '190px',
            left: '937px'
        },
        // 每一条数据
        columnData: [
            {
                name: '液位', // 标题
                data: '12.12', // 值
                unit: 'm', // 单位
                flag: '1', // 是否报警
                type: '', // 数据类型
            },
        ],
    },
    // 3 出水流量
    {
        // 数据框位置
        position: {
            top: '318px',
            left: '1382px'
        },
        // 每一条数据
        columnData: [
            {
                name: '出水流量', // 标题
                data: '123.12', // 值
                unit: 'm³/h', // 单位
                flag: '1', // 是否报警
                type: '', // 数据类型
            },
        ],
    },
])
</script>

<style lang="less" scoped>
.Configuration {
    width: 100%;
    height: 100%;
    background-image: url(/src/assets/images/configuration/jiChang/configurationBGC.png);
    background-size: 1688px 854px;
    background-repeat: no-repeat;
}
</style>

<style lang="less">
.is-disabled {
    background: #ccc !important;
    color: gray !important;
}
.is-disabled:hover {
    color: gray !important;
}
</style>