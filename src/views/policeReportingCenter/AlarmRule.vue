<template>
    <div class="AlarmRule">

        <a-form class="AlarmRuleSearchQuery" @keyup.enter.native="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
            <a-row :gutter="24">
                <a-col :span="6">
                    <a-form-item name="waterId" label="所属水厂">
                        <j-dict-select-tag placeholder="请选择所属水厂" v-model:value="queryParam.waterId" dictCode="water_works,name,id" allow-clear />
                    </a-form-item>
                </a-col>
                <a-col :span="6">
                    <a-form-item name="name" label="规则名称">
                        <a-input placeholder="请输入规则名称" v-model:value="queryParam.name" allow-clear />
                    </a-form-item>
                </a-col>
                <a-col :span="6">
                    <a-form-item name="variableName" label="变量名称">
                        <a-input placeholder="请输入变量名称" v-model:value="queryParam.variableName" allow-clear />
                    </a-form-item>
                </a-col>
                <a-col :span="6">
                    <a-form-item name="pointId" label="监测点">
                        <a-select v-model:value="queryParam.pointId" style="width: 100%;" show-search :filter-option="filterOption2" placeholder="请选择监测点">
                            <a-select-option v-for="(item, i) in selectOption" :key="i" :value="item.id">{{ item.name }}</a-select-option>
                        </a-select>
                    </a-form-item>
                </a-col>

                <a-col :span="24" style="margin-bottom: 16px;">
                    <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery">查询</a-button>
                    <a-button type="primary" preIcon="ant-design:reload-outlined" @click="searchReset" style="margin-left: 8px">重置</a-button>
                </a-col>

                <a-col :span="24" style="margin-bottom: 8px;">
                    <a-button type="primary" preIcon="ant-design:plus-outlined" @click="handleAdd">新增</a-button>
                </a-col>

            </a-row>
        </a-form>

        <!-- 外层表格 -->
        <div class="AlarmRuleTable">
            <a-table :columns="backColumns" :data-source="backData" :scroll="{ x: '100%' }" :pagination="pagination" @change="updaTable">
                <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'operation'">
                        <a-popconfirm title="确认启用？" ok-text="是" cancel-text="否" @confirm="backRun(record)">
                            <a v-if="record.status_dictText === '禁用'">启用</a>
                        </a-popconfirm>
                        <a-popconfirm title="确认禁用？" ok-text="是" cancel-text="否" @confirm="backClose(record)">
                            <a v-if="record.status_dictText === '启用'">禁用</a>
                        </a-popconfirm>
                        <a-divider type="vertical" />
                        <a @click="backEdit(record)">编辑</a>
                        <a-divider type="vertical" />
                        <a @click="backOpen(record)">详情</a>
                        <a-divider type="vertical" />
                        <a-popconfirm title="确认删除？" ok-text="是" cancel-text="否" @confirm="backDele(record)">
                            <a>删除</a>
                        </a-popconfirm>
                    </template>
                </template>
            </a-table>
        </div>

        <a-modal v-model:open="isOpenAdd" title="新增" @ok="handleOk" @cancel="handleCancel">
            <a-form class="AlarmRuleSearchQuery" ref="formRef" @keyup.enter.native="searchQuery" :model="addData" :label-col="labelCol" :wrapper-col="wrapperCol" :rules="rules">
                <a-row style="margin-top: 16px;">
                    <a-col :span="12">
                        <a-form-item name="waterId" label="所属水厂">
                            <j-dict-select-tag v-model:value="addData.waterId" dictCode="water_works,name,id" placeholder="请选择所属水厂" allow-clear />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item name="name" label="规则名称">
                            <a-input v-model:value="addData.name" placeholder="请输入规则名称" allow-clear />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item name="category" label="报警类别">
                            <j-dict-select-tag v-model:value="addData.category" dictCode="warning_type" placeholder="请选择报警类别" allow-clear />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item name="time" label="时间区间">
                            <a-time-range-picker v-model:value="addData.time" style="width: 100%;" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item name="varId" label="变量">
                            <a-select v-model:value="addData.varId" style="width: 100%" placeholder="请选择变量" show-search :filter-option="filterOption">
                                <a-select-option v-for="(item, i) in varIdSelectData" :key="i" :value="item.id">{{ item.variableNote }}</a-select-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item name="pointId" label="监测点">
                            <!-- <j-dict-select-tag v-model:value="addData.pointId" dictCode="relation_monitor,name,id" placeholder="请选择监测点" allow-clear /> -->
                            <a-select v-model:value="addData.pointId" style="width: 100%;" show-search :filter-option="filterOption2" placeholder="请选择监测点">
                                <a-select-option v-for="(item, i) in selectOption" :key="i" :value="item.id">{{ item.name }}</a-select-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col class="PumpFormIntro" :span="24">
                        <a-form-item name="remark" label="备注">
                            <a-textarea v-model:value="addData.remark" :auto-size="{ minRows: 5, maxRows: 10 }" placeholder="请输入备注" allow-clear />
                        </a-form-item>
                    </a-col>
                    <a-col class="PumpFormIntro" :span="24">
                        <a-form-item name="remark" label="比较规则">
                            <a-button type="primary" @click="addTableData">添加</a-button>
                        </a-form-item>
                    </a-col>
                    <a-col class="PumpFormIntro" :span="24">
                        <a-table :columns="columns" :data-source="addData.warningRuleInfoList" :scroll="{ x: '100%' }" :pagination="false">
                            <template #bodyCell="{ column, record, index }">
                                <template v-if     ="column.key === 'comparisonMethod'">
                                    <div style="min-height: 120px">
                                        <a-select v-model:value="record.comparisonMethod" style="width: 100%;" placeholder="请选择比较方式">
                                            <a-select-option v-for="(item, i) in comparisonMode" :key="i" :value="item.value">{{ item.label }}</a-select-option>
                                        </a-select>
                                    </div>
                                </template>
                                <template v-else-if="column.key === 'comparisonValue'">
                                    <div style="min-height: 120px">
                                        <a-input-number v-model:value="record.comparisonValue" placeholder="请输入比较值" style="width: 100%;" :disabled="!record.comparisonMethod || record.comparisonMethod === 'IN' || record.comparisonMethod === 'NOT_IN'" />
                                    </div>
                                </template>
                                <template v-else-if="column.key === 'upperLimit'">
                                    <div style="min-height: 120px">
                                        <a-input-number v-model:value="record.upperLimit" placeholder="请输入上限" style="width: 100%;" :disabled="!record.comparisonMethod || record.comparisonMethod === 'EQ' || record.comparisonMethod === 'GT' || record.comparisonMethod === 'LT'" />
                                    </div>
                                </template>
                                <template v-else-if="column.key === 'lowerLimit'">
                                    <div style="min-height: 120px">
                                        <a-input-number v-model:value="record.lowerLimit" placeholder="请输入下限" style="width: 100%;" :disabled="!record.comparisonMethod || record.comparisonMethod === 'EQ' || record.comparisonMethod === 'GT' || record.comparisonMethod === 'LT'" />
                                    </div>
                                </template>
                                <template v-else-if="column.key === 'sendMode'">
                                    <div style="min-height: 120px">
                                        <j-dict-select-tag v-model:value="record.sendMode" dictCode="warning_message_type_enum" placeholder="请选择推送方式" :disabled="!record.comparisonMethod" style="width: 100%;" mode="multiple" allow-clear />
                                    </div>
                                </template>
                                <template v-else-if="column.key === 'sendUserId'">
                                    <div style="min-height: 120px">
                                        <a-select v-model:value="record.sendUserId" style="width: 100%;" show-search placeholder="请选择推送人" mode="multiple" :disabled="!record.comparisonMethod">
                                            <a-select-option v-for="(item, i) in userSelect" :key="i" :value="item.id">{{ item.realname }}</a-select-option>
                                        </a-select>
                                    </div>
                                </template>
                                <template v-else-if="column.key === 'snOrder'">
                                    <div style="min-height: 120px">
                                        <a-input-number v-model:value="record.snOrder" placeholder="请输入比较优先级" :disabled="!record.comparisonMethod" style="width: 100%;" />
                                    </div>
                                </template>
                                <template v-else-if="column.key === 'warningCycle'">
                                    <div style="min-height: 120px">
                                        <j-dict-select-tag v-model:value="record.warningCycle" dictCode="warning_cycle_enum" :disabled="!record.comparisonMethod" placeholder="请选择报警周期类型" style="width: 100%;" allow-clear />
                                    </div>
                                </template>
                                <template v-else-if="column.key === 'warningValue'">
                                    <div style="min-height: 120px">
                                        <a-input-number v-model:value="record.warningValue" placeholder="请输入报警周期值" :disabled="!record.comparisonMethod" style="width: 100%;" />
                                    </div>
                                </template>
                                <template v-else-if="column.key === 'messageTempId'">
                                    <div style="min-height: 120px">
                                        <a-select v-model:value="record.messageTempId" style="width: 100%;" show-search placeholder="请选择报警模板" :disabled="!record.comparisonMethod">
                                            <a-select-option v-for="(item, i) in alarmRuleTemplateData" :key="i" :value="item.id">{{ item.name }}</a-select-option>
                                        </a-select>
                                    </div>
                                </template>
                                <template v-else-if="column.key === 'operation'">
                                    <a @click="dele(index)">删除</a>
                                </template>
                            </template>
                        </a-table>
                    </a-col>
                </a-row>
            </a-form>
        </a-modal>

        <a-modal v-model:open="isEdit" title="编辑" @ok="handleEditOk" @cancel="handleEditCancel">
            <a-form class="AlarmRuleSearchQuery" ref="formRef" @keyup.enter.native="searchQuery" :model="editData" :label-col="labelCol" :wrapper-col="wrapperCol" :rules="rules">
                <a-row style="margin-top: 16px;">
                    <a-col :span="12">
                        <a-form-item name="waterId" label="所属水厂">
                            <j-dict-select-tag v-model:value="editData.waterId" dictCode="water_works,name,id" placeholder="请选择所属水厂" allow-clear />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item name="name" label="规则名称">
                            <a-input v-model:value="editData.name" placeholder="请输入规则名称" allow-clear />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item name="category" label="报警类别">
                            <j-dict-select-tag v-model:value="editData.category" dictCode="warning_type" placeholder="请选择报警类别" allow-clear />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item name="time" label="时间区间">
                            <a-time-range-picker v-model:value="editData.time" style="width: 100%;" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item name="varId" label="变量">
                            <a-select v-model:value="editData.varId" style="width: 100%" placeholder="请选择变量" show-search :filter-option="filterOption">
                                <a-select-option v-for="(item, i) in varIdSelectData" :key="i" :value="item.id">{{ item.variableNote }}</a-select-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item name="pointId" label="监测点">
                            <!-- <j-dict-select-tag v-model:value="addData.pointId" dictCode="relation_monitor,name,id" placeholder="请选择监测点" allow-clear /> -->
                            <a-select v-model:value="editData.pointId" style="width: 100%;" show-search :filter-option="filterOption2" placeholder="请选择监测点">
                                <a-select-option v-for="(item, i) in selectOption" :key="i" :value="item.id">{{ item.name }}</a-select-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col class="PumpFormIntro" :span="24">
                        <a-form-item name="remark" label="备注">
                            <a-textarea v-model:value="editData.remark" :auto-size="{ minRows: 5, maxRows: 10 }" placeholder="请输入备注" allow-clear />
                        </a-form-item>
                    </a-col>
                    <a-col class="PumpFormIntro" :span="24">
                        <a-form-item name="remark" label="比较规则">
                            <a-button type="primary" @click="addEditTableData">添加</a-button>
                        </a-form-item>
                    </a-col>
                    <a-col class="PumpFormIntro" :span="24">
                        <a-table :columns="columns" :data-source="editData.warningRuleInfoList" :scroll="{ x: '100%' }" :pagination="false">
                            <template #bodyCell="{ column, record, index }">
                                <template v-if     ="column.key === 'comparisonMethod'">
                                    <div style="min-height: 120px">
                                        <j-dict-select-tag v-model:value="record.comparisonMethod" dictCode="warning_comparison_method_enum" placeholder="请选择比较方式" style="width: 100%;" allow-clear />
                                    </div>
                                </template>
                                <template v-else-if="column.key === 'comparisonValue'">
                                    <div style="min-height: 120px">
                                        <a-input-number v-model:value="record.comparisonValue" placeholder="请输入比较值" style="width: 100%;" :disabled="!record.comparisonMethod || record.comparisonMethod === 'IN' || record.comparisonMethod === 'NOT_IN'" />
                                    </div>
                                </template>
                                <template v-else-if="column.key === 'upperLimit'">
                                    <div style="min-height: 120px">
                                        <a-input-number v-model:value="record.upperLimit" placeholder="请输入上限" style="width: 100%;" :disabled="!record.comparisonMethod || record.comparisonMethod === 'EQ' || record.comparisonMethod === 'GT' || record.comparisonMethod === 'LT'" />
                                    </div>
                                </template>
                                <template v-else-if="column.key === 'lowerLimit'">
                                    <div style="min-height: 120px">
                                        <a-input-number v-model:value="record.lowerLimit" placeholder="请输入下限" style="width: 100%;" :disabled="!record.comparisonMethod || record.comparisonMethod === 'EQ' || record.comparisonMethod === 'GT' || record.comparisonMethod === 'LT'" />
                                    </div>
                                </template>
                                <template v-else-if="column.key === 'sendMode'">
                                    <div style="min-height: 120px">
                                        <j-dict-select-tag v-model:value="record.sendMode" dictCode="warning_message_type_enum" placeholder="请选择推送方式" :disabled="!record.comparisonMethod" style="width: 100%;" mode="multiple" allow-clear />
                                    </div>
                                </template>
                                <template v-else-if="column.key === 'sendUserId'">
                                    <div style="min-height: 120px">
                                        <a-select v-model:value="record.sendUserId" style="width: 100%;" show-search placeholder="请输入推送人" mode="multiple" :disabled="!record.comparisonMethod">
                                            <a-select-option v-for="(item, i) in userSelect" :key="i" :value="item.id">{{ item.realname }}</a-select-option>
                                        </a-select>
                                    </div>
                                </template>
                                <template v-else-if="column.key === 'snOrder'">
                                    <div style="min-height: 120px">
                                        <a-input-number v-model:value="record.snOrder" placeholder="请输入比较优先级" :disabled="!record.comparisonMethod" style="width: 100%;" />
                                    </div>
                                </template>
                                <template v-else-if="column.key === 'warningCycle'">
                                    <div style="min-height: 120px">
                                        <j-dict-select-tag v-model:value="record.warningCycle" dictCode="warning_cycle_enum" :disabled="!record.comparisonMethod" placeholder="请选择报警周期类型" style="width: 100%;" allow-clear />
                                    </div>
                                </template>
                                <template v-else-if="column.key === 'warningValue'">
                                    <div style="min-height: 120px">
                                        <a-input-number v-model:value="record.warningValue" placeholder="请输入报警周期值" :disabled="!record.comparisonMethod" style="width: 100%;" />
                                    </div>
                                </template>
                                <template v-else-if="column.key === 'messageTempId'">
                                    <div style="min-height: 120px">
                                        <a-select v-model:value="record.messageTempId" style="width: 100%;" show-search placeholder="请选择报警模板" :disabled="!record.comparisonMethod">
                                            <a-select-option v-for="(item, i) in alarmRuleTemplateData" :key="i" :value="item.id">{{ item.name }}</a-select-option>
                                        </a-select>
                                    </div>
                                </template>
                                <template v-else-if="column.key === 'operation'">
                                    <a @click="dele(index)">删除</a>
                                </template>
                            </template>
                        </a-table>
                    </a-col>
                </a-row>
            </a-form>
        </a-modal>

        <a-modal v-model:open="isOpen" title="详情">
            <a-form class="AlarmRuleSearchQuery" ref="formRef" @keyup.enter.native="searchQuery" :model="openData" :label-col="labelCol" :wrapper-col="wrapperCol" :rules="rules">
                <a-row style="margin-top: 16px;">
                    <a-col :span="12">
                        <a-form-item name="waterId" label="所属水厂">
                            <j-dict-select-tag v-model:value="openData.waterId" dictCode="water_works,name,id" allow-clear disabled />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item name="name" label="规则名称">
                            <a-input v-model:value="openData.name" allow-clear disabled />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item name="category" label="报警类别">
                            <j-dict-select-tag v-model:value="openData.category" dictCode="warning_type" allow-clear disabled />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item name="time" label="时间区间">
                            <a-time-range-picker v-model:value="openData.time" style="width: 100%;" disabled />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item name="varId" label="变量">
                            <a-select v-model:value="openData.varId" style="width: 100%" show-search :filter-option="filterOption" disabled>
                                <a-select-option v-for="(item, i) in varIdSelectData" :key="i" :value="item.id">{{ item.variableNote }}</a-select-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item name="pointId" label="监测点">
                            <a-select v-model:value="openData.pointId" style="width: 100%;" show-search :filter-option="filterOption2" disabled>
                                <a-select-option v-for="(item, i) in selectOption" :key="i" :value="item.id">{{ item.name }}</a-select-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col class="PumpFormIntro" :span="24">
                        <a-form-item name="remark" label="备注">
                            <a-textarea v-model:value="openData.remark" :auto-size="{ minRows: 5, maxRows: 10 }" allow-clear disabled />
                        </a-form-item>
                    </a-col>
                    <a-col class="PumpFormIntro" :span="24">
                        <a-table :columns="columns" :data-source="openData.warningRuleInfoList" :scroll="{ x: '100%' }" :pagination="false">
                            <template #bodyCell="{ column, record }">
                                <template v-if     ="column.key === 'comparisonMethod'">
                                    <j-dict-select-tag v-model:value="record.comparisonMethod" dictCode="warning_comparison_method_enum" style="width: 100%;" allow-clear disabled />
                                </template>
                                <template v-else-if="column.key === 'comparisonValue'">
                                    <a-input-number v-model:value="record.comparisonValue" style="width: 100%;" disabled />
                                </template>
                                <template v-else-if="column.key === 'upperLimit'">
                                    <a-input-number v-model:value="record.upperLimit" style="width: 100%;" disabled />
                                </template>
                                <template v-else-if="column.key === 'lowerLimit'">
                                    <a-input-number v-model:value="record.lowerLimit" style="width: 100%;" disabled />
                                </template>
                                <template v-else-if="column.key === 'sendMode'">
                                    <j-dict-select-tag v-model:value="record.sendMode" dictCode="warning_message_type_enum" disabled mode="multiple" allow-clear />
                                </template>
                                <template v-else-if="column.key === 'sendUserId'">
                                    <a-select v-model:value="record.sendUserId" style="width: 100%;" show-search mode="multiple" disabled>
                                        <a-select-option v-for="(item, i) in userSelect" :key="i" :value="item.id">{{ item.realname }}</a-select-option>
                                    </a-select>
                                </template>
                                <template v-else-if="column.key === 'snOrder'">
                                    <a-input-number v-model:value="record.snOrder" disabled />
                                </template>
                                <template v-else-if="column.key === 'warningCycle'">
                                    <j-dict-select-tag v-model:value="record.warningCycle" dictCode="warning_cycle_enum" disabled style="width: 100%;" allow-clear />
                                </template>
                                <template v-else-if="column.key === 'warningValue'">
                                    <a-input-number v-model:value="record.warningValue" disabled style="width: 100%;" />
                                </template>
                                <template v-else-if="column.key === 'messageTempId'">
                                    <div style="min-height: 120px">
                                        <a-select v-model:value="record.messageTempId" style="width: 100%;" show-search disabled>
                                            <a-select-option v-for="(item, i) in alarmRuleTemplateData" :key="i" :value="item.id">{{ item.name }}</a-select-option>
                                        </a-select>
                                    </div>
                                </template>
                            </template>
                        </a-table>
                    </a-col>
                </a-row>
            </a-form>

            <template #footer />
        </a-modal>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import type { Ref, Reactive } from 'vue'
import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue'
import dayjs  from 'dayjs'
import type { Dayjs } from 'dayjs'
import { getComparisonMode, getNowDataTable, getMonitoringPointSelectList, postAlarmRuleAdd, getUser, getAlarmRuleList, deleteAlarmRule, postEditAlarmRule, getAlarmRuleTemplate, putAlarmRuleState } from '@/api/engineering'
import type { Rule } from 'ant-design-vue/es/form'
import { message } from 'ant-design-vue'

const backRun = (record) => {
    putAlarmRuleState({
        id: record.id,
        status: 1
    })
    .then(e => {
        updaTable()
    })
}
const backClose = (record) => {
    putAlarmRuleState({
        id: record.id,
        status: 0
    })
    .then(e => {
        updaTable()
    })
}

const pagination = reactive({
    // 当前页码
    current: 1,
    // 每页的条数
    pageSize: 10,
    // 可切换的条数
    pageSizeOptions: ['10', '20', '30', '100', '200'],
    // 数据总数（目前并不知道真实的总数，所以先填写0，在后台查出来后再赋值）
    total: 0,
    // 是否显示每页条数选择器
    showSizeChanger: true,
    // 是否显示快速跳转选择框
    showQuickJumper: true,
    // 页码或每页条数改变的回调函数
    onChange: (page: number, pageSize?: number) => {
        pagination.current = page
        pagination.pageSize = pageSize
        updaTable()
    },
    // 每页条数改变的回调函数
    onShowSizeChange: (current: number, size: number) => {
        pagination.current = current
        pagination.pageSize = size
        updaTable()
    }
})

const isOpen = ref(false)

const openData: Ref<AddData> = ref({
    /**
     * 水厂
     */
    waterId: null,
    /**
     * 规则名称
     */
    name: null,
    /**
     * 报警类别
     */
    category: null,
    /**
     * 时间区间
     */
    time: null,
    /**
     * 时间区间 开始日期
     */
    sectionBeginTime: null,
    /**
     * 时间区间 结束日期
     */
    sectionEndTime: null,
    /**
     * 变量
     */
    varId: null,
    /**
     * 监测点
     */
    pointId: null,
    /**
     * 备注
     */
    remark: null,
    /**
     * 表格数据
     */
    warningRuleInfoList: []
})

const editData: Ref<AddData> = ref({
    /**
     * 水厂
     */
    waterId: null,
    /**
     * 规则名称
     */
    name: null,
    /**
     * 报警类别
     */
    category: null,
    /**
     * 时间区间
     */
    time: null,
    /**
     * 时间区间 开始日期
     */
    sectionBeginTime: null,
    /**
     * 时间区间 结束日期
     */
    sectionEndTime: null,
    /**
     * 变量
     */
    varId: null,
    /**
     * 监测点
     */
    pointId: null,
    /**
     * 备注
     */
    remark: null,
    /**
     * 表格数据
     */
    warningRuleInfoList: []
})

const handleEditOk = () => {
    if(editData.value.time && editData.value.time.length > 0) {
        editData.value.sectionBeginTime = editData.value.time[0].format('HH:mm:ss')
        editData.value.sectionEndTime   = editData.value.time[1].format('HH:mm:ss')
    }

    postEditAlarmRule(editData.value)
    .then(e => {
        updaTable()
        isEdit.value = false
    })
}
const handleEditCancel = () => {
    
}

const isEdit = ref(false)

const backData = ref()

/**
 * 编辑
 */
const backEdit = (record) => {
    editData.value = {
        ...record,
        warningRuleInfoList: record.warningRuleInfoList.slice()
    }
    // console.log('完整行数据：', record)

    if (record.sectionBeginTime !== '' && editData.value.sectionEndTime !== '') {
        const sectionBeginTimeArr = record.sectionBeginTime.split(":") as string[]
        const sectionEndTimeArr   = record.sectionEndTime  .split(":") as string[]

        editData.value.time = [
            dayjs().hour(Number(sectionBeginTimeArr[0])).minute(Number(sectionBeginTimeArr[1])).second(Number(sectionBeginTimeArr[2])),
            dayjs().hour(Number(sectionEndTimeArr[0]))  .minute(Number(sectionEndTimeArr[1]))  .second(Number(sectionEndTimeArr[2]))
        ]
    }
    isEdit.value = true
}
/**
 * 详情
 */
const backOpen = (record) => {
    openData.value = {
        ...record,
        warningRuleInfoList: record.warningRuleInfoList.slice()
    }

    if (record.sectionBeginTime !== '' && openData.value.sectionEndTime !== '') {
        const sectionBeginTimeArr = record.sectionBeginTime.split(":") as string[]
        const sectionEndTimeArr   = record.sectionEndTime  .split(":") as string[]

        openData.value.time = [
            dayjs().hour(Number(sectionBeginTimeArr[0])).minute(Number(sectionBeginTimeArr[1])).second(Number(sectionBeginTimeArr[2])),
            dayjs().hour(Number(sectionEndTimeArr[0]))  .minute(Number(sectionEndTimeArr[1]))  .second(Number(sectionEndTimeArr[2]))
        ]
    }

    isOpen.value = true
}
/**
 * 删除
 */
const backDele = (record) => {
    console.log('删除之前打印', record, record.id)
    deleteAlarmRule({
        id: record.id
    })
    .then(e => {
        updaTable()
    })
}

const formRef = ref()

const filterOption2 = (input: string, option: { value: string }) => {
    let yes = false

    selectOption.value.forEach(item => {
        /**
         * 找到id相同的
         */
        if (item.id === option.value) {
            /**
             * 是否包含字符串
             */
            yes = item.name.includes(input)
        }
    })

    return yes
}

const rules: Record<string, Rule[]> = {
    name: [{ required: true, message: '请输入规则名称！' }],
    category: [{ required: true, message: '请选择报警类别！' }],
    varId: [{ required: true, message: '请选择变量！' }],
}

// EQ     等于
// GT     大于
// LT     小于
// IN     介于
// NOT_IN 不介于

const filterOption = (input: string, option: { value: string }) => {
    let yes = false

    varIdSelectData.value.forEach(item => {
        /**
         * 找到id相同的
         */
        if (item.id === option.value) {
            /**
             * 是否包含字符串
             */
            yes = item.variableNote.includes(input)
        }
    })

    return yes
}

/**
 * 所有变量选择
 */
const varIdSelectData = ref()

/**
 * 删除
 */
const dele = (index: number) => {
    addData.warningRuleInfoList.splice(index, 1)
}

/**
 * 新增 对话框 确定
 */
const handleOk = () => {
    formRef.value
    .validate()
    .then(() => {
        if (addData.warningRuleInfoList.length === 0) {
            message.error('至少需要一条规则！')
        } else {
            let isTrueNum = 0
            addData.warningRuleInfoList.forEach(item => {
                if (
                    (item.comparisonMethod === 'EQ' || item.comparisonMethod === 'GT' || item.comparisonMethod === 'LT')
                    && item.comparisonValue
                    && item.sendMode.length > 0
                    && item.sendUserId.length > 0
                    && item.snOrder
                    && item.warningCycle
                    && item.warningValue
                    && item.messageTempId
                ) {
                    isTrueNum++
                } else if (
                    (item.comparisonMethod === 'IN' || item.comparisonMethod === 'NOT_IN')
                    && item.upperLimit
                    && item.lowerLimit
                    && item.sendMode.length > 0
                    && item.sendUserId.length > 0
                    && item.snOrder
                    && item.warningCycle
                    && item.warningValue
                    && item.messageTempId
                ) {
                    isTrueNum++
                } else {
                    message.error('比较规则的所有值必填')
                }
            })

            if (isTrueNum === addData.warningRuleInfoList.length) {
                if(addData.time && addData.time.length > 0) {
                    addData.sectionBeginTime = addData.time[0].format('HH:mm:ss')
                    addData.sectionEndTime   = addData.time[1].format('HH:mm:ss')
                }

                postAlarmRuleAdd(addData)
                .then(e => {
                    clearAddData()
                    isOpenAdd.value = false
                    updaTable()
                })
            }
        }
    })
    .catch(error => {
        console.log('校验未知错误', error)
    })
}
/**
 * 新增 对话框 取消
 */
const handleCancel = () => {
    clearAddData()
}

const isOpenAdd = ref(false)

const columns = [
    {
        title: '比较方式',
        dataIndex: 'comparisonMethod',
        key: 'comparisonMethod',
        width: 150
    },
    {
        title: '比较值',
        dataIndex: 'comparisonValue',
        key: 'comparisonValue',
        width: 150
    },
    {
        title: '上限',
        dataIndex: 'upperLimit',
        key: 'upperLimit',
        width: 150
    },
    {
        title: '下限',
        dataIndex: 'lowerLimit',
        key: 'lowerLimit',
        width: 150
    },
    {
        title: '推送方式',
        dataIndex: 'sendMode',
        key: 'sendMode',
        width: 150
    },
    {
        title: '推送人',
        dataIndex: 'sendUserId',
        key: 'sendUserId',
        width: 150
    },
    {
        title: '比较优先级',
        dataIndex: 'snOrder',
        key: 'snOrder',
        width: 150
    },
    {
        title: '报警周期类型',
        dataIndex: 'warningCycle',
        key: 'warningCycle',
        width: 150
    },
    {
        title: '报警周期值',
        dataIndex: 'warningValue',
        key: 'warningValue',
        width: 150
    },
    {
        title: '报警模板',
        dataIndex: 'messageTempId',
        key: 'messageTempId',
        width: 250
    },
    {
        title: '操作',
        align: "center",
        key: 'operation',
        fixed: 'right',
        width: 80,
    },
]

const backColumns = [
    {
        title: '水厂',
        dataIndex: 'waterId_dictText',
        key: 'waterId_dictText',
        // align: 'center',
        width: 150
    },
    {
        title: '规则名称',
        dataIndex: 'name',
        key: 'name',
        width: 150
    },
    {
        title: '类别',
        dataIndex: 'category_dictText',
        key: 'category_dictText',
        width: 150
    },
    {
        title: '变量',
        dataIndex: 'variableName',
        key: 'variableName',
        width: 150
    },
    {
        title: '状态',
        dataIndex: 'status_dictText',
        key: 'status_dictText',
        width: 150
    },
    {
        title: '监测点',
        dataIndex: 'pointId_dictText',
        key: 'pointId_dictText',
        width: 150
    },
    {
        title: '开始时间',
        dataIndex: 'sectionBeginTime',
        key: 'sectionBeginTime',
        width: 150
    },
    {
        title: '结束时间',
        dataIndex: 'sectionEndTime',
        key: 'sectionEndTime',
        width: 150
    },
    {
        title: '操作',
        align: "center",
        key: 'operation',
        fixed: 'right',
        width: 200,
    },
]

interface TableData {
    /**
     * 比较方式
     */
    comparisonMethod: string,
    /**
     * 比较值
     */
    comparisonValue: string,
    /**
     * 上限
     */
    upperLimit: string,
    /**
     * 下限
     */
    lowerLimit: string,
    /**
     * 推送方式
     */
    sendMode: string[],
    /**
     * 	推送人
     */
    sendUserId: string[],
    /**
     * 	比较优先级
     */
    snOrder: string,
    /**
     * 	报警周期类型
     */
    warningCycle: string,
    /**
     * 报警周期值
     */
    warningValue: string,
    /**
     * 报警模板
     */
    messageTempId: string,
}

const tableData: TableData = {
    comparisonMethod: null,
    comparisonValue: null,
    upperLimit: null,
    lowerLimit: null,
    sendMode: [],
    sendUserId: [],
    snOrder: null,
    warningCycle: null,
    warningValue: null,
    messageTempId: null,
}

interface AddData {
    /**
     * 水厂
     */
    waterId: string,
    /**
     * 规则名称
     */
    name: string,
    /**
     * 报警类别
     */
    category: string,
    /**
     * 时间区间
     */
    time: Dayjs[],
    /**
     * 时间区间 开始日期
     */
    sectionBeginTime: string,
    /**
     * 时间区间 结束日期
     */
    sectionEndTime: string,
    /**
     * 变量
     */
    varId: string,
    /**
     * 监测点
     */
    pointId: string,
    /**
     * 备注
     */
    remark: string,
    /**
     * 表格数据
     */
    warningRuleInfoList: TableData[]
}

const addData: Reactive<AddData> = reactive({
    /**
     * 水厂
     */
    waterId: null,
    /**
     * 规则名称
     */
    name: null,
    /**
     * 报警类别
     */
    category: null,
    /**
     * 时间区间
     */
    time: null,
    /**
     * 时间区间 开始日期
     */
    sectionBeginTime: null,
    /**
     * 时间区间 结束日期
     */
    sectionEndTime: null,
    /**
     * 变量
     */
    varId: null,
    /**
     * 监测点
     */
    pointId: null,
    /**
     * 备注
     */
    remark: null,
    /**
     * 表格数据
     */
    warningRuleInfoList: []
})
const clearAddData = () => {
    addData.waterId = null
    addData.name = null
    addData.category = null
    addData.time.length = 0
    addData.sectionBeginTime = null
    addData.sectionEndTime = null
    addData.varId = null
    addData.pointId = null
    addData.remark = null
    addData.warningRuleInfoList.length = 0
    addTableData()
}
/**
 * 增加一行规则空数据
 */
const addTableData = () => {
    addData.warningRuleInfoList.push(JSON.parse(JSON.stringify(tableData)))
}
/**
 * 增加一行规则空数据
 */
const addEditTableData = () => {
    editData.value.warningRuleInfoList.push(JSON.parse(JSON.stringify(tableData)))
}

interface ComparisonModeData {
    label: string
    value: string
}
const comparisonModeData: Ref<ComparisonModeData[]> = ref([])

interface SelectOption {
    id: string
    name: string
}

const selectOption: Ref<SelectOption[]> = ref([])

const userSelect = ref()

interface AlarmRuleTemplateData {
    id: string
    name: string
}

const alarmRuleTemplateData: Ref<AlarmRuleTemplateData[]> = ref()

interface ComparisonMode {
    label: string
    value: string
}

const comparisonMode: Ref<ComparisonMode[]> = ref()

onMounted(() => {
    getComparisonMode()
    .then(e => {
        comparisonMode.value = e
    })

    addTableData()

    getNowDataTable()
    .then(e => {
        varIdSelectData.value = e
    })

    getMonitoringPointSelectList()
    .then(e => {
        selectOption.value = e
    })

    getUser()
    .then(e => {
        userSelect.value = e
    })

    getAlarmRuleTemplate()
    .then(e => {
        alarmRuleTemplateData.value = e as AlarmRuleTemplateData[]
    })

    updaTable()
})

const updaTable = () => {
    getAlarmRuleList({
        current: pagination.current,
        pageNo: pagination.current,
        pageSize: pagination.pageSize,
        ...queryParam
    })
    .then(e => {
        backData.value = e.records
        pagination.total = e.total
    })
}

/**
 * 查询的数据
 */
const queryParam = reactive({
    /**
     * 水厂ID
     */
    waterId: null,
    /**
     * 规则名称
     */
    name: null,
    /**
     * 变量名称
     */
    variableName: null,
    /**
     * 监测点
     */
    pointId: null
})
/**
 * 查询
 */
const searchQuery = () => {
    updaTable()
}
/**
 * 重置
 */
const searchReset = () => {
    queryParam.name = null
    queryParam.pointId = null
    queryParam.variableName = null
    queryParam.waterId = null
    updaTable()
}
/**
 * 新增
 */
const handleAdd = () => {
    isOpenAdd.value = true
}

const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } })
const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } })
</script>

<style lang="less" scoped>
.AlarmRule {
    width: 100%;
    height: 100%;
    .AlarmRuleSearchQuery {
        background-color: white;
        padding: 12px 10px 6px 10px;
        margin: 8px;
    }
    .AlarmRuleTable {
        background-color: white;
        width: calc(100% - 16px);
        padding: 12px 10px 6px 10px;
        margin: 8px;
        // box-sizing: border-box;
    }
}
</style>