# 是否打开mock
VITE_USE_MOCK = true

# 发布路径
VITE_PUBLIC_PATH = /


# 跨域代理，您可以配置多个 ,请注意，没有换行符
VITE_PROXY = [["/api","http://*************:8015"],["/upload","http://*************:8015/upload"]]

#后台接口全路径地址(必填)
VITE_GLOB_DOMAIN_URL=http://*************:8015

#后台接口父地址(必填)
VITE_GLOB_API_URL=/api

# 接口前缀
VITE_GLOB_API_URL_PREFIX=

#微前端qiankun应用,命名必须以VITE_APP_SUB_开头,jeecg-app-1为子应用的项目名称,也是子应用的路由父路径
VITE_APP_SUB_jeecg-app-1 = '//localhost:8092'
