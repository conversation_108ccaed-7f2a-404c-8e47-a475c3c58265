# 全屏射线检测功能测试说明

## 修改内容总结

### 1. Analysis/index.vue 修改
- **动态 YSXK-ScaleDiv Props**: 根据 `isFullScreen` 状态动态切换组件属性
  - 全屏状态: `reduceW=0, reduceH=0, w=1920, h=1080`
  - 非全屏状态: `reduceW=dynamicReduceW, reduceH=60, w=dynamicW, h=854`
- **ESC 键监听**: 添加了 ESC 键监听，在全屏状态下按 ESC 可退出全屏
- **事件管理**: 在组件挂载和卸载时正确管理事件监听器

### 2. ClickRayCaster.ts 修改
- **Canvas 元素支持**: 构造函数新增可选的 `canvasElement` 参数
- **精确坐标计算**: 使用 Canvas 元素的 `getBoundingClientRect()` 获取准确的画布尺寸和位置
- **缩放兼容**: 计算相对于 Canvas 的鼠标位置，确保在缩放变换下射线检测准确

### 3. 所有 ThreeJS 组件更新
更新了以下组件中的 ClickRayCaster 实例化，传入 canvas 元素引用：
- `threeJs/index.vue`
- `threeJs/cleanWaterBasin.vue`
- `threeJs/filterRoom.vue`
- `threeJs/flocculationSettlingRoom.vue`
- `threeJs/waterPumpHouse.vue`
- `threeJs/chlorinationDosingRoom.vue`

## 功能验证步骤

### 1. 非全屏状态测试
1. 访问 Analysis 页面，确保页面正常显示
2. 验证侧边栏折叠/展开时，页面缩放正确调整
3. 点击 3D 场景中的可交互元素，确保射线检测正常工作
4. 检查控制台是否有缩放相关的日志输出

### 2. 全屏状态测试
1. 点击 `centerJump` 按钮进入全屏模式
2. 验证页面使用 1920x1080 固定尺寸
3. 点击 3D 场景中的可交互元素，确保射线检测在全屏下正常工作
4. 点击 `exit` 按钮或按 ESC 键退出全屏
5. 验证退出全屏后恢复到动态缩放状态

### 3. 边界情况测试
1. 快速切换全屏/非全屏状态
2. 在全屏状态下调整浏览器窗口大小
3. 在不同分辨率下测试射线检测精度
4. 测试多次进入/退出全屏的稳定性

## 预期行为

### 全屏状态
- YSXK-ScaleDiv 使用固定的 1920x1080 尺寸
- 射线检测基于 Canvas 实际渲染区域计算坐标
- ESC 键可以退出全屏
- exit 按钮可以退出全屏

### 非全屏状态
- YSXK-ScaleDiv 根据侧边栏状态动态调整尺寸
- 射线检测考虑缩放变换，确保点击精度
- 显示 open 按钮用于进入全屏

### 射线检测
- 在任何缩放状态下都能准确检测到 3D 对象
- 点击回调正常触发
- 控制台输出正确的检测信息

## 技术要点

1. **坐标系转换**: ClickRayCaster 现在正确处理从屏幕坐标到 Canvas 坐标的转换
2. **事件管理**: 使用 onMounted/onUnmounted 确保事件监听器正确清理
3. **响应式设计**: scaleDivProps 计算属性确保组件属性实时响应状态变化
4. **向后兼容**: ClickRayCaster 保持向后兼容，未传入 canvas 元素时回退到原有逻辑
